# Ifingo

## Overview

Ifingo is a robust RESTful API service built with Laravel 12, designed to provide backend functionality for loyalty application. It offers a comprehensive set of features for managing loyalty, users, and related data with a focus on performance, security, and scalability.

## Features

- **RESTful API Architecture**: Well-structured API endpoints following REST principles
- **Authentication & Authorization**: Secure user authentication and role-based access control
- **User Management**: User registration, profile management, and permissions
- **Data Validation**: Comprehensive request validation and error handling
- **API Documentation**: Auto-generated API documentation with examples
- **Caching**: Efficient caching mechanisms for improved performance
- **Background Jobs**: Queue-based processing for resource-intensive operations
- **Testing**: Comprehensive test suite with unit and feature tests
- **Docker Support**: Containerized development and deployment environment

## Technical Stack

- PHP 8.2+
- Laravel 12
- MySQL/PostgreSQL
- Redis for caching and queues
- Docker and Docker Compose for containerization

## Getting Started

See the [Development Setup](#development-setup) section below for instructions on setting up the project locally.

## Development Setup

### Prerequisites

- <PERSON><PERSON> and Docker Compose
- Git
- Make utility

### Installation Steps

1. **Clone the repository**
   ```bash
   git clone [repository-url]
   cd Ifingo
   ```

2. **Install Git hooks**
   ```bash
   make hooks
   ```

3. **Set up the project using Docker**
   ```bash
   make setup
   ```
   This command will:
   - Start Docker containers
   - Install composer dependencies
   - Set up the environment file
   - Create the database
   
   After setup completes, add the following to your `/etc/hosts` file:

   ```
   127.0.0.1 ifingo.local
   ```

4. **Start the application**
   ```bash
   make start
   ```

### Common Development Commands

- **Stop the application**
  ```bash
  make stop
  ```

- **Run database migrations**
  ```bash
  make migrate
  ```

- **Refresh database and seed**
  ```bash
  make migrate-refresh
  ```

- **Seed the database**
  ```bash
  make db-seed
  ```

- **Install/update dependencies**
  ```bash
  make install-dev-dep    # Install composer dependencies
  make update-dev-dep     # Update composer dependencies
  ```

- **Regenerate autoload files**
  ```bash
  make dump-autoload
  ```

### Accessing the Application

Once the setup is complete and the application is running:
- API Base URL: http://ifingo.local:8080/api/v1
- API Documentation: http://ifingo.local:8080/docs/api