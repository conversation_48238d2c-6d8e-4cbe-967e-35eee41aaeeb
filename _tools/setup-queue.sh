#!/bin/bash

# Get the absolute path to the project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"

echo "Setting up Laravel queue worker..."

# Run queue migrations if needed
docker exec app php /var/www/html/src/artisan queue:table
docker exec app php /var/www/html/src/artisan migrate

# Start the queue worker
docker exec -d app php /var/www/html/src/artisan queue:work

echo "Queue worker setup completed successfully!"
