#!/bin/sh

# Fetch the GIT diff and format it as command input:
DIFF=$(git diff-index --cached --name-only --diff-filter=ACM HEAD | grep '^src/')

# Define the Docker container name and the working directory inside the container
CONTAINER_NAME="app"

# Run GrumPHP inside the Docker container and capture the exit code
docker exec -i "$CONTAINER_NAME" bash -c "
    printf '%s\n' \"$DIFF\" | ./vendor/bin/grumphp git:pre-commit --ansi --skip-success-output --no-interaction
"
EXIT_CODE=$?

# If GrumPHP failed, show instructions for style-fix
if [ $EXIT_CODE -ne 0 ]; then
    echo ""
    echo "❌ Pre-commit checks failed!"
    echo ""
    echo "💡 To fix code style issues, run:"
    echo "   make style-fix"
    echo ""
    echo "This will automatically fix PHPCS violations in your changed files."
    echo ""
    exit $EXIT_CODE
fi