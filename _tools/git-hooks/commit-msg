#!/usr/bin/env bash

INPUT_FILE=$1
START_LINE=`head -n1 ${INPUT_FILE}`
PATTERN="^((build|chore|ci|docs|feat|fix|perf|refactor|revert|style|test)(\([^)]+\))?(!)?(: (.*\s*)*))|(Merge (.*\s*)*)|(Initial commit$)|(Notes added by \'git notes add\')"
MERGE_PATTERN="^Merge branch (.*) into (.*)$"
REMOTE_MERGE_PATTERN="^Merge remote-tracking branch (.*) into (.*)$"

if [[ "$START_LINE" =~ $MERGE_PATTERN ]]; then
    exit 0
fi

if [[ "$START_LINE" =~ $REMOTE_MERGE_PATTERN ]]; then
    exit 0
fi

if ! [[ "$START_LINE" =~ $PATTERN ]]; then
  echo "Bad commit message, see example: feat(CORE-123): add new feature"
  exit 1
fi
