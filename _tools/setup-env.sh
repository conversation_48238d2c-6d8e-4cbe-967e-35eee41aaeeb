#!/bin/bash

# Get the absolute path to the project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"

echo "Setting up Laravel environment..."

# Copy .env.example to .env if it doesn't exist
if [ ! -f "${PROJECT_ROOT}/src/.env" ]; then
    echo "Creating .env file from .env.example..."
    if [ -f "${PROJECT_ROOT}/src/.env.example" ]; then
        cp "${PROJECT_ROOT}/src/.env.example" "${PROJECT_ROOT}/src/.env"
        echo ".env file created successfully."
    else
        echo "Error: .env.example file not found."
        exit 1
    fi
else
    echo ".env file already exists."
fi

# Run Laravel commands inside the Docker container
echo "Generating application key..."
docker exec app php /var/www/html/src/artisan key:generate --ansi

echo "Clearing configuration cache..."
docker exec app php /var/www/html/src/artisan config:clear

echo "Clearing application cache..."
docker exec app php /var/www/html/src/artisan cache:clear

echo "Optimizing autoloader..."
docker exec app composer dump-autoload -o -d /var/www/html/src

# Create storage symlink if needed
echo "Creating storage symlink..."
docker exec app bash -c "php /var/www/html/src/artisan storage:link || echo 'Storage link already exists, skipping...'"

echo "Laravel environment setup completed successfully!"
