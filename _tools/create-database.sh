#!/bin/bash

# Get the absolute path to the project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"

echo "Setting up PostgreSQL database..."

# Check if database exists, create if not
docker exec db psql -U laravel -c "SELECT 1 FROM pg_database WHERE datname = 'laravel'" | grep -q 1 || \
    docker exec db psql -U laravel -c "CREATE DATABASE laravel"

echo "Creating database tables..."
docker exec app php /var/www/html/src/artisan migrate

echo "Database setup completed successfully!"
