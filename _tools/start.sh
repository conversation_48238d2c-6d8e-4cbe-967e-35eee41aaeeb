#!/bin/bash

# Get the absolute path to the project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"

# Get current user's UID and GID
CURRENT_UID=$(id -u)
CURRENT_GID=$(id -g)

echo "Building Docker images with UID=$CURRENT_UID and GID=$CURRENT_GID"

# Build and start the containers
docker compose -f "${PROJECT_ROOT}/_local/docker-compose.yaml" build \
  --build-arg uid=$CURRENT_UID \
  --build-arg user=$(whoami)

docker compose -f "${PROJECT_ROOT}/_local/docker-compose.yaml" up -d