#!/bin/bash

PHPVERSION=$(php --version | head -n 1 | cut -d " " -f 2 | cut -c 1,3)

if [[ $(echo " $PHPVERSION < 83" | bc) -eq 1 ]]; then
    echo "Please install php version >= 8.3"
    exit 1
fi

SCRIPT_PATH=$(cd `dirname "${BASH_SOURCE[0]}"` && pwd)

HOOKS_PATH=${SCRIPT_PATH}/git-hooks

for file in ${HOOKS_PATH}/*; do
    chmod +x ${file}
    symlink=${file##*/}
    echo "Installing $symlink hook"
    ln -sf "../../_tools/git-hooks/$symlink" ".git/hooks/$symlink"
done

echo "Done!"
