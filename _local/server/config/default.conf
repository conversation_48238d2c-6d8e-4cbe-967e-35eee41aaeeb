server {
    listen 80;
    server_name localhost ifingo.local;

    # Root directory for Laravel public files
    root /var/www/html/src/public;

    # Index file
    index index.php index.html index.htm;

    # Main route, serves the Laravel application
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP handling (for Laravel)
    location ~ \.php$ {
        include fastcgi_params;
        fastcgi_pass app:9000;   # PHP-FPM service on port 9000
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param DOCUMENT_ROOT $document_root;

        # Enable real IP logging (optional but useful)
        fastcgi_param REMOTE_ADDR $remote_addr;
    }

    # Allow access to .ht files (if any), which are not used in Laravel but might exist in legacy apps
    location ~ /\.ht {
        deny all;
    }

    # Set up error handling (optional)
    error_page 404 /404.html;
    location = /404.html {
        root /usr/share/nginx/html;
        internal;
    }

    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
        internal;
    }
}