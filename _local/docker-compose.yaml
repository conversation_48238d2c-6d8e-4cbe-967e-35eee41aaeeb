services:
  app:
    build:
      context: .
      dockerfile: ./app/Dockerfile
      args:
        user: ${USER:-default}
        uid: ${UID:-1000}
    image: local_php:latest
    container_name: app
    restart: always
    volumes:
#      - ../:/var/www/html/
      - ../.git:/var/www/html/.git # for GrumPHP
      - ../src:/var/www/html/src
      - ./app/config/php.ini:/usr/local/etc/php/php.ini
      - ./app/config/supervisord.conf:/etc/supervisord.conf
    environment:
      - GRUMPHP_PROJECT_DIR=/var/www/html/src
      - GRUMPHP_GIT_WORKING_DIR=/var/www/html/
      - GRUMPHP_GIT_REPOSITORY_DIR=/var/www/html/.git
      - GRUMPHP_COMPOSER_DIR=/var/www/html/src
      - GIT_DISCOVERY_ACROSS_FILESYSTEM=true
    depends_on:
      - db
      - redis
    networks:
      - app-network

  nginx:
    image: nginx:latest
    container_name: nginx
    restart: always
    ports:
      - "8080:80"
    volumes:
      - ./server/config/default.conf:/etc/nginx/conf.d/default.conf
      - ../src:/var/www/html/src
    depends_on:
      - app
    networks:
      - app-network

  db:
    image: mysql:8.0
    container_name: db
    restart: always
    ports:
      - "3307:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: laravel
      MYSQL_USER: laravel
      MYSQL_PASSWORD: laravel
    volumes:
      - royal_canin_data:/var/lib/mysql
    networks:
      - app-network

  redis:
    image: redis:7-alpine
    container_name: redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app-network

volumes:
  royal_canin_data:
    name: royal_canin_data
    driver: local
  redis_data:
    name: redis_data
    driver: local

networks:
  app-network:
    name: app-network
    driver: bridge
