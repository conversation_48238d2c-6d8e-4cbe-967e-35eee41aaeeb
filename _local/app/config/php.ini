; Basic PHP settings
memory_limit = 1024M
upload_max_filesize = 64M
post_max_size = 64M
max_execution_time = 120
max_input_time = 120
date.timezone = UTC

; Display errors and log settings for development
display_errors = On
display_startup_errors = On
log_errors = On
error_reporting = E_ALL
error_log = /var/log/php_errors.log

; PHP-FPM specific settings
cgi.fix_pathinfo = 0

; File handling
max_file_uploads = 20

; Session settings
session.cookie_secure = 0
session.cookie_httponly = 1
session.use_strict_mode = 1

; Opcache settings
opcache.enable = 1
opcache.memory_consumption = 256
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 10000
opcache.revalidate_freq = 2
opcache.validate_timestamps = 1

; Xdebug settings (optional for debugging)
; zend_extension = xdebug.so
; xdebug.mode = debug
; xdebug.start_with_request = yes
; xdebug.client_host = host.docker.internal
; xdebug.client_port = 9003
; xdebug.log_level = 0