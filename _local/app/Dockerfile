FROM php:8.3-fpm

ARG user=default
ARG uid=1000

# Install system dependencies
RUN apt-get update && apt-get install -y \
        nano vim git curl libpng-dev libonig-dev libxml2-dev zip unzip supervisor build-essential \
        libjpeg-dev libfreetype6-dev libssl-dev libcurl4-openssl-dev libpq-dev libzip-dev \
    && docker-php-ext-configure gd --with-jpeg --with-freetype \
    && docker-php-ext-install \
        pdo \
        pdo_mysql \
        pdo_pgsql \
        mbstring \
        zip \
        bcmath \
        opcache \
        curl \
        gd \
    && pecl install xdebug redis \
    && docker-php-ext-enable xdebug redis \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Get latest Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Create a system user to run Composer and Artisan commands
RUN useradd -G www-data,root -u $uid -d /home/<USER>
    && mkdir -p /home/<USER>/.composer \
    && chown -R $user:$user /home/<USER>

# Set the working directory
WORKDIR /var/www/html/src

# Set ownership of the working directory
RUN chown -R $uid:$uid /var/www/html

# Switch to the non-root user
USER $user

# Expose the default PHP-FPM port
EXPOSE 9000

# Start PHP-FPM as the main process
CMD ["php-fpm"]