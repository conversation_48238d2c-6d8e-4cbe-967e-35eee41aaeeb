.DEFAULT_GOAL := help
SHELL := /bin/bash

help:
	@echo ""
	@echo "Available tasks:"
	@echo "    hooks                		Install the git-hooks"
	@echo "    setup                		Setup the project using Docker"
	@echo "    start                		Start Docker containers"
	@echo "    stop                 		Stop Docker containers"
	@echo "    migrate              		Run the migration"
	@echo "    migrate-refresh       		Run the migration refresh"
	@echo "    db-seed       		        Run the database seeders"
	@echo "    install-dev-dep      		Install composer dependencies"
	@echo "    update-dev-dep       		Update composer dependencies"
	@echo "    dump-autoload        		Composer dump autoload"
	@echo "    code-quality-check   		Run GrumPHP manually"
	@echo "    style-fix            		Fix code style using Laravel Pint (changed files only)"
	@echo "    test                 		Run unit tests"
	@echo ""

hooks:
	./_tools/setup-git-hooks.sh

setup:
	./_tools/start.sh && \
	./_tools/composer.sh && \
	./_tools/setup-env.sh && \
	./_tools/create-database.sh && \
	echo "add the following line in the /etc/hosts" && echo "127.0.0.1 ifingo.local"

start:
	./_tools/start.sh

stop: 
	./_tools/stop.sh

migrate:
	docker exec app php artisan migrate

migrate-refresh:
	docker exec app php artisan migrate:fresh

db-seed:
	docker exec app php artisan db:seed

install-dev-dep:
	docker exec app composer install

update-dev-dep:
	docker exec app composer update

dump-autoload:
	docker exec app composer dump-autoload

code-quality-check:
	docker exec app ./vendor/bin/grumphp run --ansi --no-interaction

style-fix:
	docker exec app ./vendor/bin/pint --dirty

test:
	docker exec app php artisan test

.PHONY: hooks setup start stop migrate migrate-refresh db-seed install-dev-dep update-dev-dep dump-autoload code-quality-check style-fix test
