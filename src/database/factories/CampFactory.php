<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Camp>
 */
class CampFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $user1 = \App\Models\User::factory()->create();
        $user2 = \App\Models\User::factory()->create();
        return [
            'name' => $this->faker->company(),
            'description' => $this->faker->paragraph(),
            'start_date' => now(),
            'end_date' => now()->addDays(2),
            'location' => $this->faker->address(),
            'max_capacity' => 50,
            'price' => $this->faker->numberBetween(100, 1000),
            'status' => 'active',
        ];
    }
}
