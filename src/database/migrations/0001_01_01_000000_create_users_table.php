<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // uers table
        Schema::create('users', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('avatar_url', 255)->nullable();
            $table->string('email', 255)->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password', 255);
            $table->string('first_name', 255)->nullable();
            $table->string('last_name', 255)->nullable();
            $table->string('phone_number', 20)->nullable();
            $table->string('status')->default('active'); // active, inactive, pending_verification, banned
            $table->timestamps();
        });

        // roles table
        Schema::create('roles', function (Blueprint $table) {
            $table->id();
            $table->string('role_name', 255);
            $table->text('permissions')->nullable();
        });

        // user_roles table
        Schema::create('user_roles', function (Blueprint $table) {
            $table->uuid('user_id');
            $table->unsignedBigInteger('role_id');
            $table->primary(['user_id', 'role_id']);
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('role_id')->references('id')->on('roles')->onDelete('cascade');
        });

        // password_reset_tokens table
        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->id();
            $table->uuid('user_id');
            $table->string('token', 255);
            $table->timestamp('expires_at');
            $table->timestamp('created_at')->nullable();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });

        // user_connection_histories table
        Schema::create('user_connection_histories', function (Blueprint $table) {
            $table->id();
            $table->uuid('user_id');
            $table->dateTime('login_time');
            $table->string('ip_address', 50)->nullable();
            $table->string('device_info', 255)->nullable();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });

        // api_access_tokens table
        Schema::create('api_access_tokens', function (Blueprint $table) {
            $table->id();
            $table->uuid('user_id');
            $table->string('access_token', 255);
            $table->timestamp('expires_at');
            $table->timestamp('created_at')->nullable();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });

        // audit_logs table
        Schema::create('audit_logs', function (Blueprint $table) {
            $table->id();
            $table->uuid('user_id');
            $table->string('action', 255);
            $table->text('details')->nullable();
            $table->timestamp('timestamp');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('audit_logs');
        Schema::dropIfExists('api_access_tokens');
        Schema::dropIfExists('user_connection_histories');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('user_roles');
        Schema::dropIfExists('roles');
        Schema::dropIfExists('users');
    }
};
