<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('files', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('fileable_id')->nullable();
            $table->string('fileable_type')->nullable();
            $table->string('type', 64)->default('file');
            $table->string('path');
            $table->unsignedBigInteger('size')->nullable();
            $table->string('etag')->nullable();
            $table->string('sha256_hash')->nullable();
            $table->string('mime_type');
            $table->timestamps();

            $table->index(['fileable_id', 'fileable_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('files');
    }
};
