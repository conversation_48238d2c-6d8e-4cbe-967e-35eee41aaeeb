<?php

use App\Enums\CampStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('camps', function (Blueprint $table) {
            $table->id();
            $table->string('name', 500)->index();
            $table->string('description', 500);
            $table->text('internal_note')->nullable();
            $table->dateTime('start_date')->index();
            $table->dateTime('end_date')->index();
            $table->string('location', 255)->index();
            $table->unsignedInteger('max_capacity');
            $table->unsignedInteger('price')->default(0)->index();
            $table->string('status')->default(CampStatus::Active->value)->index();

            $table->foreignUuid('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignUuid('updated_by')->nullable()->constrained('users')->nullOnDelete();

            $table->timestamps();
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('camps');
    }
};
