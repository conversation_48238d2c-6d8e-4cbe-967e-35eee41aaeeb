<?php

declare(strict_types=1);

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Admin User
        $adminId = (string) Str::uuid();
        $adminEmail = '<EMAIL>';
        $adminPassword = Hash::make('Admin@123');

        DB::table('users')->insert([
            'id' => $adminId,
            'email' => $adminEmail,
            'password' => $adminPassword,
            'first_name' => 'Admin',
            'last_name' => 'User',
            'status' => 'active',
            'email_verified_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Ensure admin role exists
        $roleId = DB::table('roles')->insertGetId([
            'role_name' => 'admin',
            'permissions' => null,
        ]);

        // Assign admin role to user
        DB::table('user_roles')->insert([
            'user_id' => $adminId,
            'role_id' => $roleId,
        ]);

        // Ensure customer role exists
        $customerRoleId = DB::table('roles')->insertGetId([
            'role_name' => 'customer',
            'permissions' => null,
        ]);

        // Add 3 customer users
        $customers = [
            [
                'email' => '<EMAIL>',
                'first_name' => 'Customer',
                'last_name' => 'One',
            ],
            [
                'email' => '<EMAIL>',
                'first_name' => 'Customer',
                'last_name' => 'Two',
            ],
            [
                'email' => '<EMAIL>',
                'first_name' => 'Customer',
                'last_name' => 'Three',
            ],
        ];
        foreach ($customers as $customer) {
            $customerId = (string) Str::uuid();
            DB::table('users')->insert([
                'id' => $customerId,
                'email' => $customer['email'],
                'password' => Hash::make('Customer@123'),
                'first_name' => $customer['first_name'],
                'last_name' => $customer['last_name'],
                'status' => 'active',
                'email_verified_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            DB::table('user_roles')->insert([
                'user_id' => $customerId,
                'role_id' => $customerRoleId,
            ]);
        }
    }
}
