<?php

declare(strict_types=1);

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            ['role_name' => 'admin', 'permissions' => null],
            ['role_name' => 'customer', 'permissions' => null],
            ['role_name' => 'retailer', 'permissions' => null],
        ];
        foreach ($roles as $role) {
            DB::table('roles')->updateOrInsert(
                ['role_name' => $role['role_name']],
                ['permissions' => $role['permissions']]
            );
        }
    }
}
