<?php

use App\Http\ApiResponse;
use App\Http\Middleware\AddRequestId;
use App\Http\Middleware\AddResponseId;
use App\Http\Middleware\Authenticate;
use App\Http\Middleware\CheckFirstLogin;
use App\Http\Middleware\Localization;
use App\Http\Middleware\RequestLogging;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Spatie\Permission\Middleware\PermissionMiddleware;
use Spatie\Permission\Middleware\RoleMiddleware;
use Spatie\Permission\Middleware\RoleOrPermissionMiddleware;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\TooManyRequestsHttpException;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // Register custom middleware in the correct order
        $middleware->append(AddRequestId::class);
        $middleware->append(AddResponseId::class);
        $middleware->append(Localization::class);
        $middleware->append(RequestLogging::class);

        $middleware->alias([
            'role' => RoleMiddleware::class,
            'permission' => PermissionMiddleware::class,
            'role_or_permission' => RoleOrPermissionMiddleware::class,
            'portal_auth' => Authenticate::class,
            'check.first.login' => CheckFirstLogin::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->render(function (Throwable $e, Request $request) {
            if ($request->is('api/*')) {
                $debug = config('app.debug');

                if (app()->isDownForMaintenance()) {
                    return new ApiResponse(
                        __('messages.system_maintenance'),
                        false,
                        [],
                        [],
                        503
                    );
                }

                $data = [];
                if ($debug) {
                    $data = [
                        'exception' => get_class($e),
                        'file' => $e->getFile(),
                        'line' => $e->getLine(),
                    ];
                }

                // Handle authentication exceptions properly
                if ($e instanceof AuthenticationException) {
                    return new ApiResponse(
                        __('messages.unauthenticated'),
                        false,
                        $data,
                        [],
                        401
                    );
                }

                if ($e instanceof NotFoundHttpException) {
                    $message = !empty($e->getMessage())
                        ? $e->getMessage()
                        : __('messages.not_found');

                    return new ApiResponse(
                        $message,
                        false,
                        $data,
                        [],
                        $e->getStatusCode()
                    );
                }

                if ($e instanceof AccessDeniedHttpException) {
                    return new ApiResponse(
                        $e->getMessage() ?? __('messages.access_denied'),
                        false,
                        $data,
                        [],
                        403
                    );
                }

                if ($e instanceof TooManyRequestsHttpException ||
                    $e instanceof BadRequestHttpException ||
                    $e instanceof MethodNotAllowedHttpException) {
                    return new ApiResponse(
                        $e->getMessage(),
                        false,
                        $data,
                        [],
                        $e->getStatusCode()
                    );
                }

                if ($e instanceof ValidationException) {
                    // For authentication-related validation, return message codes instead of full messages
                    $errors = $e->errors();
                    $messageCodes = [];
                    
                    foreach ($errors as $field => $messages) {
                        $messageCodes[$field] = $messages;
                    }
                    
                    return new ApiResponse(
                        'validation_failed',
                        apiSuccess: false,
                        status: 422,
                        errors: $messageCodes,
                    );
                }

                $statusCode = 500;
                if ($e instanceof \Symfony\Component\HttpKernel\Exception\HttpException) {
                    $statusCode = $e->getStatusCode();
                }

                return new ApiResponse(
                    $debug ? $e->getMessage() : __('messages.unexpected_error'),
                    false,
                    $data,
                    [],
                    $statusCode
                );
            }

            return null;
        });
    })->create();
