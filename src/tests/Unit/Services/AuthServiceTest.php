<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Models\RefreshToken;
use App\Models\User;
use App\Repositories\UserRepository;
use App\Services\AuthService;
use App\Services\OtpService;
use App\Channels\EmailChannel;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Tests\Unit\UnitTestCase;

class AuthServiceTest extends UnitTestCase
{
    use RefreshDatabase;

    private AuthService $authService;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $userRepository = $this->app->make(UserRepository::class);
        $otpService = $this->createMock(OtpService::class);
        $emailChannel = $this->createMock(EmailChannel::class);

        $this->authService = new AuthService($userRepository, $otpService, $emailChannel);

        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'phone_number' => '+1234567890',
            'password' => Hash::make('password123'),
            'status' => 'active',
        ]);
    }

    public function test_login_returns_access_and_refresh_tokens(): void
    {
        $credentials = [
            'email' => $this->user->email,
            'password' => 'password123',
        ];

        $result = $this->authService->login($credentials);

        $this->assertArrayHasKey('user', $result);
        $this->assertArrayHasKey('access_token', $result);
        $this->assertArrayHasKey('refresh_token', $result);
        $this->assertArrayHasKey('token_type', $result);
        $this->assertEquals('Bearer', $result['token_type']);
        $this->assertEquals($this->user->email, $result['user']['email']);
    }

    public function test_login_fails_with_invalid_credentials(): void
    {
        $credentials = [
            'email' => $this->user->email,
            'password' => 'wrongpassword',
        ];

        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('incorrect_credentials');

        $this->authService->login($credentials);
    }

    public function test_login_fails_with_nonexistent_user(): void
    {
        $credentials = [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ];

        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('account_not_found');

        $this->authService->login($credentials);
    }

    public function test_login_fails_with_inactive_user(): void
    {
        $this->user->update(['status' => 'inactive']);

        $credentials = [
            'email' => $this->user->email,
            'password' => 'password123',
        ];

        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('account_deactivated_contact_support');

        $this->authService->login($credentials);
    }

    public function test_refresh_token_success(): void
    {
        // Create a refresh token for the user
        $tokens = $this->createRefreshTokenForUser($this->user);

        $result = $this->authService->refreshToken($tokens['refresh_token']);

        $this->assertArrayHasKey('access_token', $result);
        $this->assertArrayHasKey('refresh_token', $result);
        $this->assertArrayHasKey('user', $result);
        $this->assertArrayHasKey('token_type', $result);
        $this->assertEquals('Bearer', $result['token_type']);
        $this->assertEquals($this->user->email, $result['user']['email']);
    }

    public function test_refresh_token_invalid_token(): void
    {
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('invalid_refresh_token');

        $this->authService->refreshToken('invalid_token');
    }

    public function test_refresh_token_expired_token(): void
    {
        // Create an expired refresh token
        $refreshToken = RefreshToken::factory()->create([
            'user_id' => $this->user->id,
            'expires_at' => now()->subHour(),
        ]);

        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('refresh_token_expired');

        $this->authService->refreshToken($refreshToken->token);
    }

    public function test_refresh_token_inactive_user(): void
    {
        $this->user->update(['status' => 'inactive']);

        $tokens = $this->createRefreshTokenForUser($this->user);

        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('account_deactivated_contact_support');

        $this->authService->refreshToken($tokens['refresh_token']);
    }

    public function test_logout_removes_user_tokens(): void
    {
        // Create some tokens for the user
        $this->user->createToken('test-token-1');
        $this->user->createToken('test-token-2');

        $this->authService->logout($this->user);

        // Verify all tokens are deleted
        $this->assertEquals(0, $this->user->tokens()->count());
    }

    public function test_get_user_profile(): void
    {
        $result = $this->authService->getUserProfile($this->user);

        $this->assertArrayHasKey('user', $result);
        $this->assertEquals($this->user->email, $result['user']['email']);
        $this->assertEquals($this->user->first_name, $result['user']['first_name']);
        $this->assertEquals($this->user->last_name, $result['user']['last_name']);
    }

    public function test_update_profile_success(): void
    {
        $updateData = [
            'first_name' => 'Updated',
            'last_name' => 'Name',
            'email' => '<EMAIL>',
            'phone_number' => '+9876543210',
        ];

        $result = $this->authService->updateProfile($this->user, $updateData);

        $this->assertArrayHasKey('user', $result);
        $this->assertEquals('Updated', $result['user']['first_name']);
        $this->assertEquals('Name', $result['user']['last_name']);
        $this->assertEquals('<EMAIL>', $result['user']['email']);
        $this->assertEquals('+9876543210', $result['user']['phone_number']);
    }

    public function test_update_profile_fails_with_duplicate_email(): void
    {
        // Create another user with the email we want to use
        User::factory()->create(['email' => '<EMAIL>']);

        $updateData = [
            'email' => '<EMAIL>',
        ];

        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('validation.email_already_taken');

        $this->authService->updateProfile($this->user, $updateData);
    }

    public function test_update_profile_fails_with_duplicate_phone(): void
    {
        // Create another user with the phone we want to use
        User::factory()->create(['phone_number' => '+9876543210']);

        $updateData = [
            'phone_number' => '+9876543210',
        ];

        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('validation.phone_already_taken');

        $this->authService->updateProfile($this->user, $updateData);
    }

    public function test_update_password_success(): void
    {
        $result = $this->authService->updatePassword($this->user, 'password123', 'newpassword123');

        $this->assertArrayHasKey('success', $result);
        $this->assertTrue($result['success']);

        // Verify password was actually changed
        $this->user->refresh();
        $this->assertTrue(Hash::check('newpassword123', $this->user->password));
    }

    public function test_update_password_fails_with_wrong_current_password(): void
    {
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('current_password');

        $this->authService->updatePassword($this->user, 'wrongpassword', 'newpassword123');
    }

    public function test_attempt_panel_login_success(): void
    {
        $result = $this->authService->attemptPanelLogin('<EMAIL>', 'password123');

        $this->assertArrayHasKey('user', $result);
        $this->assertArrayHasKey('access_token', $result);
        $this->assertArrayHasKey('token_type', $result);
        $this->assertEquals('Bearer', $result['token_type']);
        $this->assertEquals($this->user->email, $result['user']['email']);
    }

    public function test_attempt_panel_login_fails_with_invalid_credentials(): void
    {
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('incorrect_credentials');

        $this->authService->attemptPanelLogin('<EMAIL>', 'wrongpassword');
    }

    public function test_attempt_panel_login_fails_with_inactive_user(): void
    {
        $this->user->update(['status' => 'inactive']);

        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('account_deactivated_contact_support');

        $this->authService->attemptPanelLogin('<EMAIL>', 'password123');
    }

    public function test_get_current_user(): void
    {
        $result = $this->authService->getCurrentUser();

        $this->assertInstanceOf(User::class, $result);
        $this->assertEquals($this->user->id, $result->id);
    }

    private function createRefreshTokenForUser(User $user): array
    {
        $refreshToken = RefreshToken::factory()->create([
            'user_id' => $user->id,
            'expires_at' => now()->addDays(30),
        ]);

        return [
            'refresh_token' => $refreshToken->token,
            'access_token' => $user->createToken('test-token')->plainTextToken,
        ];
    }
}
