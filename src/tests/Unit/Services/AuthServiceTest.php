<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Models\RefreshToken;
use App\Models\User;
use App\Repositories\UserRepository;
use App\Services\AuthService;
use App\Services\OtpService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Tests\Unit\UnitTestCase;

class AuthServiceTest extends UnitTestCase
{
    use RefreshDatabase;

    private AuthService $authService;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $userRepository = $this->app->make(UserRepository::class);
        $otpService = $this->createMock(OtpService::class);

        $this->authService = new AuthService($userRepository, $otpService);

        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'password' => Hash::make('password123'),
            'is_active' => true,
        ]);
    }

    public function test_register_returns_refresh_token()
    {
        $userData = [
            'user_type' => 'customer',
            'full_name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '+1987654321',
            'password' => 'password123',
            'date_of_birth' => '1990-01-01',
            'address' => '123 Test St',
            'ward' => 'Test Ward',
            'district' => 'Test District',
            'city' => 'Test City',
        ];

        $result = $this->authService->register($userData);

        $this->assertArrayHasKey('refresh_token', $result);
        $this->assertNotEmpty($result['refresh_token']);
        $this->assertIsString($result['refresh_token']);
        $this->assertEquals('Bearer', $result['token_type']);

        // Verify refresh token is stored in database
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertEquals(1, RefreshToken::where('user_id', $user->id)->count());
    }

    public function test_login_returns_refresh_token()
    {
        $credentials = [
            'phone' => $this->user->phone,
            'password' => 'password123',
        ];

        $result = $this->authService->login($credentials);

        $this->assertArrayHasKey('refresh_token', $result);
        $this->assertNotEmpty($result['refresh_token']);
        $this->assertIsString($result['refresh_token']);
        $this->assertEquals('Bearer', $result['token_type']);

        // Verify refresh token is stored in database
        $this->assertEquals(1, RefreshToken::where('user_id', $this->user->id)->count());
    }

    public function test_refresh_token_success()
    {
        // Create a refresh token for the user
        $tokens = $this->createRefreshTokenForUser($this->user);

        // Mock the request to include the Authorization header
        request()->headers->set('Authorization', 'Bearer ' . $tokens['access_token']);

        $result = $this->authService->refreshToken($tokens['refresh_token']);

        $this->assertArrayHasKey('access_token', $result);
        $this->assertArrayHasKey('refresh_token', $result);
        $this->assertArrayHasKey('user', $result);
        $this->assertArrayHasKey('token_type', $result);

        $this->assertNotEmpty($result['access_token']);
        $this->assertNotEmpty($result['refresh_token']);
        $this->assertEquals('Bearer', $result['token_type']);

        // Verify the new refresh token is different
        $this->assertNotEquals($tokens['refresh_token'], $result['refresh_token']);

        // Verify only one refresh token exists (old one replaced)
        $this->assertEquals(1, RefreshToken::where('user_id', $this->user->id)->count());
    }

    public function test_refresh_token_invalid_token()
    {
        // Create a valid access token for the header
        $accessTokenObject = $this->user->createToken('test-token');
        request()->headers->set('Authorization', 'Bearer ' . $accessTokenObject->plainTextToken);

        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('No refresh token found for this access token.');

        $this->authService->refreshToken('invalid_token_that_is_long_enough');
    }

    public function test_refresh_token_expired_token()
    {
        // Create an expired refresh token
        $accessTokenObject = $this->user->createToken('expired-test-token');
        $accessTokenId = $accessTokenObject->accessToken->id;

        $plainTextToken = str()->random(80);
        RefreshToken::create([
            'user_id' => $this->user->id,
            'access_token_id' => $accessTokenId,
            'token' => Hash::make($plainTextToken),
            'expires_at' => now()->subDay(),
        ]);

        // Set the authorization header with the access token
        request()->headers->set('Authorization', 'Bearer ' . $accessTokenObject->plainTextToken);

        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('No refresh token found for this access token.');

        $this->authService->refreshToken($plainTextToken);
    }

    public function test_refresh_token_inactive_user()
    {
        // Create a refresh token for the user
        $tokenData = $this->createRefreshTokenForUser($this->user);
        $plainTextToken = $tokenData['refresh_token'];

        // Set the authorization header with the access token
        request()->headers->set('Authorization', 'Bearer ' . $tokenData['access_token']);

        // Deactivate the user
        $this->user->update(['is_active' => false]);

        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('User account has been deactivated.');

        $this->authService->refreshToken($plainTextToken);
    }

    public function test_logout_removes_only_current_device_tokens()
    {
        // Create multiple refresh tokens for the user (simulating multiple devices)
        $device1Tokens = $this->createRefreshTokenForUser($this->user);
        $device2Tokens = $this->createRefreshTokenForUser($this->user);

        // Verify both tokens exist
        $this->assertEquals(2, RefreshToken::where('user_id', $this->user->id)->count());

        // Simulate login on device 1 by setting current access token
        $device1AccessToken = \Laravel\Sanctum\PersonalAccessToken::findToken(
            explode('|', $device1Tokens['access_token'])[1]
        );
        $this->user->withAccessToken($device1AccessToken);

        // Logout from device 1
        $this->authService->logout($this->user);

        // Verify only device 1's refresh token is removed, device 2 remains
        $this->assertEquals(1, RefreshToken::where('user_id', $this->user->id)->count());

        // Verify the remaining token is device 2's token
        $remainingToken = RefreshToken::where('user_id', $this->user->id)->first();
        $this->assertNotNull($remainingToken);

        // Get device 2's access token ID for comparison
        $device2AccessToken = \Laravel\Sanctum\PersonalAccessToken::findToken(
            explode('|', $device2Tokens['access_token'])[1]
        );
        $this->assertEquals($device2AccessToken->id, $remainingToken->access_token_id);

        // Verify device 1's access token is also deleted
        $this->assertDatabaseMissing('personal_access_tokens', ['id' => $device1AccessToken->id]);
        // Verify device 2's access token still exists
        $this->assertDatabaseHas('personal_access_tokens', ['id' => $device2AccessToken->id]);
    }

    public function test_logout_handles_missing_refresh_token_gracefully()
    {
        // Create access token without associated refresh token
        $accessTokenObject = $this->user->createToken('test-token');
        $this->user->withAccessToken($accessTokenObject->accessToken);

        // Should not throw exception when no refresh token exists
        $this->authService->logout($this->user);

        // Verify access token is still deleted
        $this->assertDatabaseMissing('personal_access_tokens', ['id' => $accessTokenObject->accessToken->id]);
    }

    public function test_refresh_token_rotation_security()
    {
        // Create initial refresh token
        $tokenData = $this->createRefreshTokenForUser($this->user);
        $originalRefreshToken = $tokenData['refresh_token'];

        // Set the authorization header with the access token
        request()->headers->set('Authorization', 'Bearer ' . $tokenData['access_token']);

        // Use refresh token
        $result = $this->authService->refreshToken($originalRefreshToken);
        $newRefreshToken = $result['refresh_token'];

        // Verify the original token can't be used again
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('No refresh token found for this access token.');

        $this->authService->refreshToken($originalRefreshToken);
    }

    public function test_generate_refresh_token_creates_database_record()
    {
        // Create a mock access token
        $accessTokenObject = $this->user->createToken('test-token');
        $accessTokenId = $accessTokenObject->accessToken->id;

        // Use reflection to access private method for testing
        $reflection = new \ReflectionClass($this->authService);
        $method = $reflection->getMethod('generateRefreshToken');
        $method->setAccessible(true);

        $refreshToken = $method->invoke($this->authService, $this->user, $accessTokenId);

        $this->assertIsString($refreshToken);
        $this->assertGreaterThan(50, strlen($refreshToken));

        // Verify database record was created
        $this->assertEquals(1, RefreshToken::where('user_id', $this->user->id)->count());

        $tokenRecord = RefreshToken::where('user_id', $this->user->id)->first();
        $this->assertNotNull($tokenRecord);
        $this->assertEquals($accessTokenId, $tokenRecord->access_token_id);
        $this->assertTrue(Hash::check($refreshToken, $tokenRecord->token));
        $this->assertTrue($tokenRecord->expires_at->isFuture());
    }

    public function test_refresh_token_model_relationships()
    {
        $plainTextToken = $this->createRefreshTokenForUser($this->user);

        $refreshToken = RefreshToken::where('user_id', $this->user->id)->first();

        // Test relationship
        $this->assertEquals($this->user->id, $refreshToken->user->id);
        $this->assertTrue($this->user->refreshTokens->contains($refreshToken));
    }

    public function test_refresh_token_cleanup_expired()
    {
        // Create valid and expired tokens
        $this->createRefreshTokenForUser($this->user);

        $expiredAccessTokenObject = $this->user->createToken('expired-cleanup-test-token');
        $expiredAccessTokenId = $expiredAccessTokenObject->accessToken->id;

        RefreshToken::create([
            'user_id' => $this->user->id,
            'access_token_id' => $expiredAccessTokenId,
            'token' => Hash::make('expired_token'),
            'expires_at' => now()->subDay(),
        ]);

        $this->assertEquals(2, RefreshToken::count());

        // Clean up expired tokens
        $deletedCount = RefreshToken::cleanupExpired();

        $this->assertEquals(1, $deletedCount);
        $this->assertEquals(1, RefreshToken::count());

        // Verify only valid token remains
        $remaining = RefreshToken::first();
        $this->assertTrue($remaining->isValid());
    }

    // ==================== FORGOT PASSWORD TESTS ====================

    public function test_initiate_forgot_password_with_registered_phone(): void
    {
        $otpService = $this->createMock(OtpService::class);
        $otpService->expects($this->once())
            ->method('generatePasswordResetOtp')
            ->with('+1234567890')
            ->willReturn('123456');

        $otpService->expects($this->once())
            ->method('getPasswordResetRemainingAttempts')
            ->with('+1234567890')
            ->willReturn(3);

        $userRepository = $this->app->make(UserRepository::class);
        $authService = new AuthService($userRepository, $otpService);

        $result = $authService->initiateForgotPassword('+1234567890');

        $this->assertArrayHasKey('phone', $result);
        $this->assertArrayHasKey('expires_in', $result);
        $this->assertArrayHasKey('remaining_attempts', $result);
        $this->assertEquals('+1234567890', $result['phone']);
        $this->assertEquals(180, $result['expires_in']); // 3 minutes
        $this->assertEquals(3, $result['remaining_attempts']);
    }

    public function test_initiate_forgot_password_with_unregistered_phone(): void
    {
        $otpService = $this->createMock(OtpService::class);
        $userRepository = $this->app->make(UserRepository::class);
        $authService = new AuthService($userRepository, $otpService);

        $this->expectException(ValidationException::class);

        $authService->initiateForgotPassword('+9999999999');
    }

    public function test_initiate_forgot_password_with_deactivated_user(): void
    {
        // Create a deactivated user
        $deactivatedUser = User::factory()->create([
            'phone' => '+9876543210',
            'is_active' => false,
        ]);

        $otpService = $this->createMock(OtpService::class);
        $userRepository = $this->app->make(UserRepository::class);
        $authService = new AuthService($userRepository, $otpService);

        $this->expectException(ValidationException::class);

        $authService->initiateForgotPassword('+9876543210');
    }

    public function test_verify_password_reset_otp_with_valid_data(): void
    {
        $otpService = $this->createMock(OtpService::class);
        $otpService->expects($this->once())
            ->method('verifyPasswordResetOtp')
            ->with('+1234567890', '123456')
            ->willReturn(true);

        $userRepository = $this->app->make(UserRepository::class);
        $authService = new AuthService($userRepository, $otpService);

        $result = $authService->verifyPasswordResetOtp('+1234567890', '123456');

        $this->assertArrayHasKey('phone', $result);
        $this->assertArrayHasKey('verified', $result);
        $this->assertArrayHasKey('reset_token', $result);
        $this->assertArrayHasKey('reset_token_expires_in', $result);
        $this->assertEquals('+1234567890', $result['phone']);
        $this->assertTrue($result['verified']);
        $this->assertEquals(600, $result['reset_token_expires_in']);
    }

    public function test_verify_password_reset_otp_with_unregistered_phone(): void
    {
        $otpService = $this->createMock(OtpService::class);
        $otpService->expects($this->once())
            ->method('verifyPasswordResetOtp')
            ->with('+9999999999', '123456')
            ->willReturn(true);

        $userRepository = $this->app->make(UserRepository::class);
        $authService = new AuthService($userRepository, $otpService);

        $this->expectException(ValidationException::class);

        $authService->verifyPasswordResetOtp('+9999999999', '123456');
    }

    public function test_verify_password_reset_otp_with_deactivated_user(): void
    {
        // Create a deactivated user
        $deactivatedUser = User::factory()->create([
            'phone' => '+9876543210',
            'is_active' => false,
        ]);

        $otpService = $this->createMock(OtpService::class);
        $otpService->expects($this->once())
            ->method('verifyPasswordResetOtp')
            ->with('+9876543210', '123456')
            ->willReturn(true);

        $userRepository = $this->app->make(UserRepository::class);
        $authService = new AuthService($userRepository, $otpService);

        $this->expectException(ValidationException::class);

        $authService->verifyPasswordResetOtp('+9876543210', '123456');
    }

    public function test_reset_password_with_valid_token(): void
    {
        $otpService = $this->createMock(OtpService::class);
        $otpService->expects($this->once())
            ->method('clearPasswordResetOtp')
            ->with('+1234567890');

        $userRepository = $this->app->make(UserRepository::class);
        $authService = new AuthService($userRepository, $otpService);

        // Create a valid reset token directly using reflection to access private method
        $reflection = new \ReflectionClass($authService);
        $generateTokenMethod = $reflection->getMethod('generatePasswordResetToken');
        $generateTokenMethod->setAccessible(true);
        $resetToken = $generateTokenMethod->invoke($authService, '+1234567890');

        $result = $authService->resetPassword('+1234567890', $resetToken, 'NewPassword123!');

        $this->assertArrayHasKey('success', $result);
        $this->assertArrayHasKey('message', $result);
        $this->assertTrue($result['success']);

        // Verify password was actually changed
        $this->user->refresh();
        $this->assertTrue(Hash::check('NewPassword123!', $this->user->password));
    }

    public function test_reset_password_with_invalid_token(): void
    {
        $otpService = $this->createMock(OtpService::class);
        $userRepository = $this->app->make(UserRepository::class);
        $authService = new AuthService($userRepository, $otpService);

        $this->expectException(ValidationException::class);

        $authService->resetPassword('+1234567890', 'invalid_token', 'NewPassword123!');
    }

    public function test_reset_password_with_mismatched_phone_and_token(): void
    {
        $otpService = $this->createMock(OtpService::class);
        $userRepository = $this->app->make(UserRepository::class);
        $authService = new AuthService($userRepository, $otpService);

        // Create a valid reset token for one phone number
        $reflection = new \ReflectionClass($authService);
        $generateTokenMethod = $reflection->getMethod('generatePasswordResetToken');
        $generateTokenMethod->setAccessible(true);
        $resetToken = $generateTokenMethod->invoke($authService, '+1234567890');

        $this->expectException(ValidationException::class);

        // Try to use the token with a different phone number
        $authService->resetPassword('+9999999999', $resetToken, 'NewPassword123!');
    }

    /**
     * Helper method to create a refresh token for a user
     */
    private function createRefreshTokenForUser(User $user): array
    {
        // Create access token first
        $accessTokenObject = $user->createToken('test-token');
        $accessToken = $accessTokenObject->plainTextToken;
        $accessTokenId = $accessTokenObject->accessToken->id;

        $plainTextToken = str()->random(80);

        RefreshToken::create([
            'user_id' => $user->id,
            'access_token_id' => $accessTokenId,
            'token' => Hash::make($plainTextToken),
            'expires_at' => now()->addDays(30),
        ]);

        return [
            'access_token' => $accessToken,
            'refresh_token' => $plainTextToken,
        ];
    }
}
