<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Models\User;
use App\Repositories\UserRepository;
use App\Services\AuthService;
use App\Services\OtpService;
use Database\Seeders\RoleSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Tests\Unit\UnitTestCase;

class AuthServicePortalTest extends UnitTestCase
{
    use RefreshDatabase;

    protected AuthService $authService;

    protected UserRepository $userRepository;

    protected OtpService $otpService;

    protected function setUp(): void
    {
        parent::setUp();

        // Run role seeder to create admin role
        $this->seed(RoleSeeder::class);

        $this->userRepository = $this->app->make(UserRepository::class);
        $this->otpService = $this->app->make(OtpService::class);
        $smsChannel = $this->app->make(\App\Channels\SmsChannel::class);
        $this->authService = new AuthService($this->userRepository, $this->otpService, $smsChannel);
    }

    public function test_attempt_portal_login_success_with_valid_credentials(): void
    {
        // Create admin user
        $adminUser = User::factory()->admin()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'is_active' => true,
        ]);
        $adminUser->assignRole('admin');

        $result = $this->authService->attemptPanelLogin('<EMAIL>', 'Test@123');

        $this->assertTrue($result['success']);
        $this->assertFalse($result['is_first_login']);
        $this->assertEquals($adminUser->id, $result['user']->id);
        $this->assertAuthenticatedAs($adminUser);
    }

    public function test_attempt_portal_login_success_with_first_time_login(): void
    {
        // Create admin user with first_login_completed = false
        $adminUser = User::factory()->admin()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'is_active' => true,
            'first_login_completed' => false,
        ]);
        $adminUser->assignRole('admin');

        $result = $this->authService->attemptPanelLogin('<EMAIL>', 'Test@123');

        $this->assertTrue($result['success']);
        $this->assertTrue($result['is_first_login']);
        $this->assertEquals($adminUser->id, $result['user']->id);
        $this->assertAuthenticatedAs($adminUser);
    }

    public function test_attempt_portal_login_fails_with_invalid_password(): void
    {
        // Create admin user
        $adminUser = User::factory()->admin()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'is_active' => true,
        ]);
        $adminUser->assignRole('admin');

        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('Provided credentials are incorrect.');

        $this->authService->attemptPanelLogin('<EMAIL>', 'wrongpassword');
    }

    public function test_attempt_portal_login_throws_exception_for_nonexistent_user(): void
    {
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('Provided credentials are incorrect.');

        $this->authService->attemptPanelLogin('<EMAIL>', 'Test@123');
    }

    public function test_attempt_portal_login_throws_exception_for_inactive_user(): void
    {
        // Create inactive admin user
        $inactiveAdmin = User::factory()->admin()->inactive()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
        ]);
        $inactiveAdmin->assignRole('admin');

        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('Your account has been deactivated. Please contact support.');

        $this->authService->attemptPanelLogin('<EMAIL>', 'Test@123');
    }

    public function test_attempt_portal_login_throws_exception_for_non_admin_user(): void
    {
        // Create customer user
        $customer = User::factory()->customer()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'is_active' => true,
        ]);

        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('You do not have access.');

        $this->authService->attemptPanelLogin('<EMAIL>', 'Test@123');
    }

    public function test_attempt_portal_login_updates_last_login_timestamp(): void
    {
        // Create admin user
        $adminUser = User::factory()->admin()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'is_active' => true,
            'last_login_at' => null,
        ]);
        $adminUser->assignRole('admin');

        $this->assertNull($adminUser->last_login_at);

        $this->authService->attemptPanelLogin('<EMAIL>', 'Test@123');

        $adminUser->refresh();
        $this->assertNotNull($adminUser->last_login_at);
    }

    public function test_attempt_portal_login_with_remember_me(): void
    {
        // Create admin user
        $adminUser = User::factory()->admin()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'is_active' => true,
        ]);
        $adminUser->assignRole('admin');

        $result = $this->authService->attemptPanelLogin('<EMAIL>', 'Test@123', true);

        $this->assertTrue($result['success']);
        $this->assertAuthenticatedAs($adminUser);
    }

    public function test_is_admin_returns_true_for_admin_user(): void
    {
        $adminUser = User::factory()->admin()->create();
        $adminUser->assignRole('admin');

        $this->assertTrue($this->authService->isAdmin($adminUser));
    }

    public function test_is_admin_returns_false_for_customer_user(): void
    {
        $customerUser = User::factory()->customer()->create();

        $this->assertFalse($this->authService->isAdmin($customerUser));
    }

    public function test_is_admin_returns_false_for_user_without_role(): void
    {
        $userWithoutRole = User::factory()->admin()->create();
        // Don't assign admin role

        $this->assertFalse($this->authService->isAdmin($userWithoutRole));
    }

    public function test_logout_portal_logs_out_user(): void
    {
        $adminUser = User::factory()->admin()->create();
        $adminUser->assignRole('admin');

        Auth::login($adminUser);
        $this->assertAuthenticatedAs($adminUser);

        $this->authService->logoutAdmin();

        $this->assertGuest();
    }

    public function test_get_current_user_returns_authenticated_user(): void
    {
        $adminUser = User::factory()->admin()->create();
        $adminUser->assignRole('admin');

        Auth::login($adminUser);

        $currentUser = $this->authService->getCurrentUser();

        $this->assertEquals($adminUser->id, $currentUser->id);
    }
}
