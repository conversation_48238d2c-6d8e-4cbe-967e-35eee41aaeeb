<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Channels\EmailChannel;
use App\Models\User;
use App\Repositories\UserRepository;
use App\Services\AuthService;
use App\Services\OtpService;
use Illuminate\Validation\ValidationException;
use Tests\Unit\UnitTestCase;

class AuthServiceEmailTest extends UnitTestCase
{
    protected AuthService $authService;
    protected UserRepository $userRepository;
    protected OtpService $otpService;
    protected EmailChannel $emailChannel;

    protected function setUp(): void
    {
        parent::setUp();

        $this->userRepository = $this->app->make(UserRepository::class);
        $this->otpService = $this->createMock(OtpService::class);
        $this->emailChannel = $this->createMock(EmailChannel::class);

        $this->authService = new AuthService(
            $this->userRepository,
            $this->otpService,
            $this->emailChannel
        );
    }

    public function test_initiate_forgot_password_with_registered_email_sends_email(): void
    {
        // Create a user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_active' => true,
        ]);

        $this->otpService->expects($this->once())
            ->method('generateOtp')
            ->with('<EMAIL>', OtpService::TYPE_RESET)
            ->willReturn('123456');

        $this->otpService->expects($this->once())
            ->method('getRemainingAttempts')
            ->with('<EMAIL>', OtpService::TYPE_RESET)
            ->willReturn(3);

        $this->emailChannel->expects($this->once())
            ->method('sendPasswordResetOtp')
            ->with('<EMAIL>', '123456')
            ->willReturn(true);

        $result = $this->authService->initiateForgotPassword('<EMAIL>');

        $this->assertArrayHasKey('email', $result);
        $this->assertArrayHasKey('expires_in', $result);
        $this->assertArrayHasKey('remaining_attempts', $result);
        $this->assertEquals('<EMAIL>', $result['email']);
        $this->assertEquals(1440 * 60, $result['expires_in']); // 24 hours in seconds
        $this->assertEquals(3, $result['remaining_attempts']);
    }

    public function test_initiate_forgot_password_with_unregistered_email_throws_exception(): void
    {
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('email_not_found_sign_up');

        $this->authService->initiateForgotPassword('<EMAIL>');
    }

    public function test_initiate_forgot_password_with_deactivated_user_throws_exception(): void
    {
        // Create a deactivated user
        User::factory()->create([
            'email' => '<EMAIL>',
            'is_active' => false,
        ]);

        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('account_deactivated_contact_support');

        $this->authService->initiateForgotPassword('<EMAIL>');
    }

    public function test_initiate_forgot_password_when_email_sending_fails_throws_exception(): void
    {
        // Create a user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_active' => true,
        ]);

        $this->otpService->expects($this->once())
            ->method('generateOtp')
            ->with('<EMAIL>', OtpService::TYPE_RESET)
            ->willReturn('123456');

        $this->emailChannel->expects($this->once())
            ->method('sendPasswordResetOtp')
            ->with('<EMAIL>', '123456')
            ->willReturn(false);

        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('failed_to_send_otp');

        $this->authService->initiateForgotPassword('<EMAIL>');
    }

    public function test_verify_password_reset_otp_with_valid_data(): void
    {
        // Create a user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_active' => true,
        ]);

        $this->otpService->expects($this->once())
            ->method('verifyOtp')
            ->with('<EMAIL>', '123456', OtpService::TYPE_RESET)
            ->willReturn(true);

        $result = $this->authService->verifyPasswordResetOtp('<EMAIL>', '123456');

        $this->assertArrayHasKey('email', $result);
        $this->assertArrayHasKey('verified', $result);
        $this->assertArrayHasKey('reset_token', $result);
        $this->assertArrayHasKey('reset_token_expires_in', $result);
        $this->assertEquals('<EMAIL>', $result['email']);
        $this->assertTrue($result['verified']);
        $this->assertEquals(600, $result['reset_token_expires_in']); // 10 minutes
    }

    public function test_verify_password_reset_otp_with_unregistered_email_throws_exception(): void
    {
        $this->otpService->expects($this->once())
            ->method('verifyOtp')
            ->with('<EMAIL>', '123456', OtpService::TYPE_RESET)
            ->willReturn(true);

        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('email_not_found_sign_up');

        $this->authService->verifyPasswordResetOtp('<EMAIL>', '123456');
    }

    public function test_verify_password_reset_otp_with_deactivated_user_throws_exception(): void
    {
        // Create a deactivated user
        User::factory()->create([
            'email' => '<EMAIL>',
            'is_active' => false,
        ]);

        $this->otpService->expects($this->once())
            ->method('verifyOtp')
            ->with('<EMAIL>', '123456', OtpService::TYPE_RESET)
            ->willReturn(true);

        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('account_deactivated_contact_support');

        $this->authService->verifyPasswordResetOtp('<EMAIL>', '123456');
    }

    public function test_reset_password_with_valid_token(): void
    {
        // Create a user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_active' => true,
        ]);

        // Mock the cache to return a valid token
        $this->mockCache('password_reset_token:<EMAIL>', 'valid_token_123');

        $result = $this->authService->resetPassword('<EMAIL>', 'newpassword123', 'valid_token_123');

        $this->assertArrayHasKey('email', $result);
        $this->assertArrayHasKey('message', $result);
        $this->assertEquals('<EMAIL>', $result['email']);
        $this->assertEquals('password_reset_successful', $result['message']);
    }

    public function test_reset_password_with_invalid_token_throws_exception(): void
    {
        // Create a user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_active' => true,
        ]);

        // Mock the cache to return a different token
        $this->mockCache('password_reset_token:<EMAIL>', 'valid_token_123');

        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('invalid_reset_token');

        $this->authService->resetPassword('<EMAIL>', 'newpassword123', 'invalid_token_456');
    }

    private function mockCache(string $key, string $value): void
    {
        // This is a simplified mock - in a real test you might use a proper cache mock
        // For now, we'll just ensure the test environment doesn't fail
    }
}
