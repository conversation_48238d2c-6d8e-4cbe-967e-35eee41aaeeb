<?php

declare(strict_types=1);

namespace Tests\Unit\Helpers;

use App\Helpers\CacheHelper;
use Tests\Unit\UnitTestCase;

class CacheHelperTest extends UnitTestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        CacheHelper::clear();
    }

    public function test_set_and_get_value(): void
    {
        $key = 'test_key';
        $value = 'test_value';

        CacheHelper::set($key, $value, 60);
        $result = CacheHelper::get($key);

        $this->assertEquals($value, $result);
    }

    public function test_get_with_default_value(): void
    {
        $key = 'non_existent_key';
        $default = 'default_value';

        $result = CacheHelper::get($key, $default);

        $this->assertEquals($default, $result);
    }

    public function test_has_key(): void
    {
        $key = 'test_key';
        $value = 'test_value';

        $this->assertFalse(CacheHelper::has($key));

        CacheHelper::set($key, $value);
        $this->assertTrue(CacheHelper::has($key));
    }

    public function test_delete_key(): void
    {
        $key = 'test_key';
        $value = 'test_value';

        CacheHelper::set($key, $value);
        $this->assertTrue(CacheHelper::has($key));

        CacheHelper::delete($key);
        $this->assertFalse(CacheHelper::has($key));
    }

    public function test_increment_and_decrement(): void
    {
        $key = 'counter';

        // Test increment
        $result = CacheHelper::increment($key);
        $this->assertEquals(1, $result);

        $result = CacheHelper::increment($key, 5);
        $this->assertEquals(6, $result);

        // Test decrement
        $result = CacheHelper::decrement($key, 2);
        $this->assertEquals(4, $result);
    }

    public function test_clear_all(): void
    {
        $key1 = 'key1';
        $key2 = 'key2';

        CacheHelper::set($key1, 'value1');
        CacheHelper::set($key2, 'value2');

        $this->assertTrue(CacheHelper::has($key1));
        $this->assertTrue(CacheHelper::has($key2));

        CacheHelper::clear();

        $this->assertFalse(CacheHelper::has($key1));
        $this->assertFalse(CacheHelper::has($key2));
    }

    public function test_get_multiple(): void
    {
        $keys = ['key1', 'key2', 'key3'];
        $values = ['value1', 'value2', 'value3'];

        foreach ($keys as $index => $key) {
            CacheHelper::set($key, $values[$index]);
        }

        $results = CacheHelper::getMultiple($keys);

        $this->assertEquals($values[0], $results['key1']);
        $this->assertEquals($values[1], $results['key2']);
        $this->assertEquals($values[2], $results['key3']);
    }

    public function test_set_multiple(): void
    {
        $values = [
            'key1' => 'value1',
            'key2' => 'value2',
            'key3' => 'value3',
        ];

        $result = CacheHelper::setMultiple($values);

        $this->assertTrue($result);
        $this->assertTrue(CacheHelper::has('key1'));
        $this->assertTrue(CacheHelper::has('key2'));
        $this->assertTrue(CacheHelper::has('key3'));
    }

    public function test_delete_multiple(): void
    {
        $keys = ['key1', 'key2', 'key3'];

        foreach ($keys as $key) {
            CacheHelper::set($key, 'value');
        }

        $result = CacheHelper::deleteMultiple($keys);

        $this->assertTrue($result);
        $this->assertFalse(CacheHelper::has('key1'));
        $this->assertFalse(CacheHelper::has('key2'));
        $this->assertFalse(CacheHelper::has('key3'));
    }

    public function test_remember(): void
    {
        $key = 'remember_key';
        $callback = function () {
            return 'callback_value';
        };

        $result = CacheHelper::remember($key, 60, $callback);

        $this->assertEquals('callback_value', $result);
        $this->assertTrue(CacheHelper::has($key));
    }

    public function test_remember_forever(): void
    {
        $key = 'forever_key';
        $callback = function () {
            return 'forever_value';
        };

        $result = CacheHelper::rememberForever($key, $callback);

        $this->assertEquals('forever_value', $result);
        $this->assertTrue(CacheHelper::has($key));
    }
}
