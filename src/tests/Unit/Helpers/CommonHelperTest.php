<?php

declare(strict_types=1);

namespace Tests\Unit\Helpers;

use App\Helpers\ApiMessages;
use App\Helpers\Common;
use Tests\Unit\UnitTestCase;

class CommonHelperTest extends UnitTestCase
{
    public function test_translation_function_returns_string(): void
    {
        $result = Common::___('test.message', [], 'response-messages');

        $this->assertIsString($result);
    }

    public function test_translation_with_replacements(): void
    {
        $replacements = ['name' => 'John'];
        $result = Common::___('test.message', $replacements, 'response-messages');

        $this->assertIsString($result);
    }

    public function test_translation_with_custom_locale(): void
    {
        $result = Common::___('test.message', [], 'response-messages', 'en');

        $this->assertIsString($result);
    }

    public function test_make_api_message_returns_api_messages_instance(): void
    {
        $result = Common::makeApiMessage('test.message', [], 'response-messages');

        $this->assertInstanceOf(ApiMessages::class, $result);
    }

    public function test_make_api_message_with_replacements(): void
    {
        $replacements = ['name' => 'John'];
        $result = Common::makeApiMessage('test.message', $replacements, 'response-messages');

        $this->assertInstanceOf(ApiMessages::class, $result);
    }

    public function test_make_api_message_with_custom_source(): void
    {
        $result = Common::makeApiMessage('test.message', [], 'custom-source');

        $this->assertInstanceOf(ApiMessages::class, $result);
    }

    public function test_translation_handles_empty_key(): void
    {
        $result = Common::___('', [], 'response-messages');

        $this->assertIsString($result);
    }

    public function test_make_api_message_handles_empty_key(): void
    {
        $result = Common::makeApiMessage('', [], 'response-messages');

        $this->assertInstanceOf(ApiMessages::class, $result);
    }

    public function test_translation_with_null_locale(): void
    {
        $result = Common::___('test.message', [], 'response-messages', null);

        $this->assertIsString($result);
    }

    public function test_make_api_message_with_empty_replacements(): void
    {
        $result = Common::makeApiMessage('test.message', [], 'response-messages');

        $this->assertInstanceOf(ApiMessages::class, $result);
    }
}
