<?php

declare(strict_types=1);

namespace Tests\Unit\Controllers;

use App\Http\Controllers\API\V1\AuthController;
use App\Http\Requests\API\V1\Auth\LoginRequest;
use App\Services\AuthService;
use Illuminate\Validation\ValidationException;
use Tests\Unit\UnitTestCase;

class AuthControllerTest extends UnitTestCase
{
    private AuthController $authController;

    private $authService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->authService = $this->mock(AuthService::class);
        $this->authController = new AuthController($this->authService);
    }

    public function test_login_validates_request_data(): void
    {
        $request = $this->mock(LoginRequest::class);
        $request->shouldReceive('only')
            ->with('email', 'password')
            ->andReturn([
                'email' => '',
                'password' => '',
            ]);

        $this->authService
            ->shouldReceive('login')
            ->once()
            ->andThrow(new ValidationException(validator([])));

        $response = $this->authController->login($request);

        $this->assertEquals(401, $response->getStatusCode());
        $this->assertArrayHasKey('data', $response->getData(true));
    }

    public function test_login_calls_auth_service_when_valid(): void
    {
        $request = $this->mock(LoginRequest::class);
        $request->shouldReceive('only')
            ->with('email', 'password')
            ->andReturn([
                'email' => '<EMAIL>',
                'password' => 'password123',
            ]);

        $this->authService
            ->shouldReceive('login')
            ->once()
            ->andReturn([
                'user' => ['id' => '123', 'full_name' => 'John Doe'],
                'access_token' => 'token',
                'token_type' => 'Bearer',
            ]);

        $response = $this->authController->login($request);

        $this->assertEquals(200, $response->getStatusCode());
        $responseData = $response->getData(true);
        $this->assertTrue($responseData['success']);
        $this->assertEquals('User logged in successfully', $responseData['message']);
    }

    public function test_login_handles_validation_exception(): void
    {
        $request = $this->mock(LoginRequest::class);
        $request->shouldReceive('only')
            ->with('email', 'password')
            ->andReturn([
                'email' => '<EMAIL>',
                'password' => 'wrongpassword',
            ]);

        $this->authService
            ->shouldReceive('login')
            ->once()
            ->andThrow(new ValidationException(validator([])));

        $response = $this->authController->login($request);

        $this->assertEquals(401, $response->getStatusCode());
        $responseData = $response->getData(true);
        $this->assertFalse($responseData['success']);
        $this->assertEquals('Invalid credentials', $responseData['message']);
    }
}
