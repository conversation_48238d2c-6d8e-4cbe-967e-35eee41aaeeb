<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Rules\PhoneNumber;
use Illuminate\Support\Facades\App;

class PhoneNumberRuleTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        // Mock PhoneNumberUtil service
        $this->mockPhoneNumberUtil();
    }

    private function mockPhoneNumberUtil()
    {
        $phoneUtil = \Mockery::mock('PhoneNumberUtil');

        // Mock French phone number validation
        $frenchNumber = \Mockery::mock();
        $phoneUtil->shouldReceive('parse')
            ->with('+33123456789')
            ->andReturn($frenchNumber);
        $phoneUtil->shouldReceive('isValidNumber')
            ->with($frenchNumber)
            ->andReturn(true);
        $phoneUtil->shouldReceive('getRegionCodeForNumber')
            ->with($frenchNumber)
            ->andReturn('FR');
        $phoneUtil->shouldReceive('format')
            ->with($frenchNumber, \libphonenumber\PhoneNumberFormat::E164)
            ->andReturn('+33123456789');

        // Mock non-French phone number (should fail)
        $nonFrenchNumber = \Mockery::mock();
        $phoneUtil->shouldReceive('parse')
            ->with('+84901234567')
            ->andReturn($nonFrenchNumber);
        $phoneUtil->shouldReceive('isValidNumber')
            ->with($nonFrenchNumber)
            ->andReturn(true);
        $phoneUtil->shouldReceive('getRegionCodeForNumber')
            ->with($nonFrenchNumber)
            ->andReturn('VN');
        $phoneUtil->shouldReceive('format')
            ->with($nonFrenchNumber, \libphonenumber\PhoneNumberFormat::E164)
            ->andReturn('+84901234567');

        // Mock invalid phone number
        $invalidNumber = \Mockery::mock();
        $phoneUtil->shouldReceive('parse')
            ->with('+123456789')
            ->andReturn($invalidNumber);
        $phoneUtil->shouldReceive('isValidNumber')
            ->with($invalidNumber)
            ->andReturn(false);

        // Mock non-E164 format
        $nonE164Number = \Mockery::mock();
        $phoneUtil->shouldReceive('parse')
            ->with('33123456789')
            ->andThrow(new \libphonenumber\NumberParseException(1, 'Invalid number'));

        App::instance('PhoneNumberUtil', $phoneUtil);
    }

    public function test_valid_french_phone_number_passes_validation()
    {
        $rule = new PhoneNumber();
        $failed = false;
        $failCallback = function ($message) use (&$failed) {
            $failed = true;
        };

        $rule->validate('phone_number', '+33123456789', $failCallback);

        $this->assertFalse($failed, 'Valid French phone number should pass validation');
    }

    public function test_non_french_phone_number_fails_validation()
    {
        $rule = new PhoneNumber();
        $failed = false;
        $failCallback = function ($message) use (&$failed) {
            $failed = true;
        };

        $rule->validate('phone_number', '+84901234567', $failCallback);

        $this->assertTrue($failed, 'Non-French phone number should fail validation');
    }

    public function test_invalid_phone_number_fails_validation()
    {
        $rule = new PhoneNumber();
        $failed = false;
        $failCallback = function ($message) use (&$failed) {
            $failed = true;
        };

        $rule->validate('phone_number', '+123456789', $failCallback);

        $this->assertTrue($failed, 'Invalid phone number should fail validation');
    }

    public function test_phone_number_without_plus_prefix_fails_validation()
    {
        $rule = new PhoneNumber();
        $failed = false;
        $failCallback = function ($message) use (&$failed) {
            $failed = true;
        };

        $rule->validate('phone_number', '33123456789', $failCallback);

        $this->assertTrue($failed, 'Phone number without + prefix should fail validation');
    }

    protected function tearDown(): void
    {
        \Mockery::close();
        parent::tearDown();
    }
}
