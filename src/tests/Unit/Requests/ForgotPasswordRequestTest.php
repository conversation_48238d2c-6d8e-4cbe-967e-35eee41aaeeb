<?php

declare(strict_types=1);

namespace Tests\Unit\Requests;

use App\Helpers\Utils;
use App\Http\Requests\Portal\Auth\ForgotPasswordRequest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\Unit\UnitTestCase;

class ForgotPasswordRequestTest extends UnitTestCase
{
    use RefreshDatabase;

    public function test_prepare_for_validation_normalizes_french_phone_numbers(): void
    {
        $request = new ForgotPasswordRequest;

        // Test cases for French phone number normalization
        $testCases = [
            '0123456789' => '+330123456789',
            '330123456789' => '+330123456789',
            '+330123456789' => '+330123456789',
            '123456789' => '+33123456789',
            '0987654321' => '+330987654321',
            '330987654321' => '+330987654321',
            '+330987654321' => '+330987654321',
        ];

        foreach ($testCases as $input => $expected) {
            $request->merge(['phone' => $input]);
            $request->prepareForValidation();

            $this->assertEquals($expected, $request->input('phone'));
        }
    }

    public function test_prepare_for_validation_handles_invalid_phone_numbers(): void
    {
        $request = new ForgotPasswordRequest;

        // Test cases that should not be normalized (return null)
        $invalidCases = [
            '123', // Too short
            '123456789012345', // Too long
            '+1234567890', // Not French
            'abc123def', // Contains letters
            '', // Empty
        ];

        foreach ($invalidCases as $input) {
            $request->merge(['phone' => $input]);
            $request->prepareForValidation();

            // Should not change the input if normalization fails
            $this->assertEquals($input, $request->input('phone'));
        }
    }

    public function test_prepare_for_validation_handles_missing_phone(): void
    {
        $request = new ForgotPasswordRequest;

        // Should not throw any exception when phone is missing
        $request->prepareForValidation();

        $this->assertNull($request->input('phone'));
    }

    public function test_utils_normalize_french_phone_number_method(): void
    {
        // Test the helper method directly
        $testCases = [
            '0123456789' => '+330123456789',
            '330123456789' => '+330123456789',
            '+330123456789' => '+330123456789',
            '123456789' => '+33123456789',
            '0987654321' => '+330987654321',
            '330987654321' => '+330987654321',
            '+330987654321' => '+330987654321',
            ' 0123456789 ' => '+330123456789', // With spaces
            '0123-456-789' => '+330123456789', // With dashes
            '0123 456 789' => '+330123456789', // With spaces
        ];

        foreach ($testCases as $input => $expected) {
            $result = Utils::normalizeFrenchPhoneNumber($input);
            $this->assertEquals($expected, $result, "Failed for input: {$input}");
        }

        // Test invalid cases
        $invalidCases = [
            '123',
            '123456789012345',
            '+1234567890',
            'abc123def',
            '',
            null,
        ];

        foreach ($invalidCases as $input) {
            $result = Utils::normalizeFrenchPhoneNumber($input);
            $this->assertNull($result, "Should return null for invalid input: {$input}");
        }
    }
}
