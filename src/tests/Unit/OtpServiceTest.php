<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Channels\SmsChannel;
use App\Services\OtpService;
use Illuminate\Validation\ValidationException;
use Tests\TestCase;

class OtpServiceTest extends TestCase
{
    protected OtpService $otpService;

    protected SmsChannel $mockChannel;

    protected function setUp(): void
    {
        parent::setUp();

        // Create mock channel
        $this->mockChannel = $this->createMock(SmsChannel::class);
        $this->otpService = new OtpService;
        $this->otpService->setOtpChannel($this->mockChannel);
    }

    public function test_generate_otp_creates_valid_otp()
    {
        $phone = '+1234567890';

        $this->mockChannel
            ->expects($this->once())
            ->method('canSend')
            ->with($phone)
            ->willReturn(true);

        $this->mockChannel
            ->expects($this->once())
            ->method('sendOtp')
            ->with($phone, $this->matchesRegularExpression('/^\d{6}$/'))
            ->willReturn(true);

        $otp = $this->otpService->generateOtp($phone);

        $this->assertIsString($otp);
        $this->assertEquals(6, strlen($otp));
        $this->assertTrue(is_numeric($otp));
    }

    public function test_verify_otp_with_valid_otp_returns_true()
    {
        $phone = '+1234567890';
        $otp = '123456';

        $this->mockChannel
            ->expects($this->once())
            ->method('canAttempt')
            ->with($phone)
            ->willReturn(true);

        $this->mockChannel
            ->expects($this->once())
            ->method('verifyOtp')
            ->with($phone, $otp)
            ->willReturn(true);

        $result = $this->otpService->verifyOtp($phone, $otp);
        $this->assertTrue($result);
    }

    public function test_verify_otp_with_invalid_otp_throws_exception()
    {
        $phone = '+1234567890';

        $this->mockChannel
            ->expects($this->once())
            ->method('canAttempt')
            ->with($phone)
            ->willReturn(true);

        $this->mockChannel
            ->expects($this->once())
            ->method('verifyOtp')
            ->with($phone, '000000')
            ->willReturn(false);

        $this->expectException(ValidationException::class);
        $this->otpService->verifyOtp($phone, '000000');
    }

    public function test_rate_limiting_prevents_too_many_otp_requests()
    {
        $phone = '+1234567890';

        $this->mockChannel
            ->expects($this->once())
            ->method('canSend')
            ->with($phone)
            ->willReturn(false);

        $this->expectException(ValidationException::class);
        $this->otpService->generateOtp($phone);
    }

    public function test_get_remaining_attempts_returns_correct_count()
    {
        $phone = '+1234567890';

        $this->mockChannel
            ->expects($this->once())
            ->method('getAttemptCount')
            ->with($phone)
            ->willReturn(1);

        $result = $this->otpService->getRemainingAttempts($phone);
        $this->assertEquals(2, $result); // 3 - 1 = 2
    }

    public function test_admin_send_otp_to_phone_works_correctly()
    {
        $phone = '+1234567890';

        $this->mockChannel
            ->expects($this->once())
            ->method('canSend')
            ->with($phone)
            ->willReturn(true);

        $this->mockChannel
            ->expects($this->once())
            ->method('sendOtp')
            ->with($phone, $this->matchesRegularExpression('/^\d{6}$/'))
            ->willReturn(true);

        // Should not throw exception
        $this->otpService->generateOtp($phone, OtpService::TYPE_REGISTER);
    }

    public function test_admin_verify_otp_for_phone_works_correctly()
    {
        $phone = '+1234567890';
        $otp = '123456';

        $this->mockChannel
            ->expects($this->once())
            ->method('canAttempt')
            ->with($phone)
            ->willReturn(true);

        $this->mockChannel
            ->expects($this->once())
            ->method('verifyOtp')
            ->with($phone, $otp)
            ->willReturn(true);

        // Should not throw exception
        $this->otpService->verifyOtp($phone, $otp, OtpService::TYPE_REGISTER);
    }

    public function test_admin_verify_otp_for_phone_with_invalid_otp_throws_exception()
    {
        $phone = '+1234567890';

        $this->mockChannel
            ->expects($this->once())
            ->method('canAttempt')
            ->with($phone)
            ->willReturn(true);

        $this->mockChannel
            ->expects($this->once())
            ->method('verifyOtp')
            ->with($phone, '000000')
            ->willReturn(false);

        // Should throw exception with invalid OTP
        $this->expectException(ValidationException::class);
        $this->otpService->verifyOtp($phone, '000000', OtpService::TYPE_REGISTER);
    }

    public function test_get_remaining_send_attempts_returns_correct_count()
    {
        $phone = '+1234567890';

        $this->mockChannel
            ->expects($this->once())
            ->method('getSendCount')
            ->with($phone)
            ->willReturn(2);

        $result = $this->otpService->getRemainingSendAttempts($phone);
        $this->assertEquals(3, $result); // 5 - 2 = 3
    }

    public function test_can_send_otp_returns_true()
    {
        $phone = '+1234567890';

        $this->mockChannel
            ->expects($this->once())
            ->method('canSend')
            ->with($phone)
            ->willReturn(true);

        $result = $this->otpService->canSendOtp($phone);
        $this->assertTrue($result);
    }

    public function test_can_attempt_returns_true()
    {
        $phone = '+1234567890';

        $this->mockChannel
            ->expects($this->once())
            ->method('canAttempt')
            ->with($phone)
            ->willReturn(true);

        $result = $this->otpService->canAttempt($phone);
        $this->assertTrue($result);
    }
}
