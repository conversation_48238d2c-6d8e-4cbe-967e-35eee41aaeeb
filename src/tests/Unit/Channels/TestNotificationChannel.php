<?php

declare(strict_types=1);

namespace Tests\Unit\Channels;

use App\Channels\AbstractNotificationChannel;

/**
 * Test implementation of AbstractNotificationChannel
 */
class TestNotificationChannel extends AbstractNotificationChannel
{
    private bool $shouldSucceed;

    private bool $shouldThrowException;

    public function __construct(bool $shouldSucceed = true, bool $shouldThrowException = false)
    {
        $this->shouldSucceed = $shouldSucceed;
        $this->shouldThrowException = $shouldThrowException;
    }

    public function testIncrementAttempt(string $destination): void
    {
        $this->incrementAttempt($destination);
    }

    protected function sendNotification(string $destination, string $message, array $options = []): bool
    {
        if ($this->shouldThrowException) {
            throw new \Exception('Test exception');
        }

        return $this->shouldSucceed;
    }
}
