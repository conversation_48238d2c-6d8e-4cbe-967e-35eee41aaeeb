<?php

declare(strict_types=1);

namespace Tests\Unit\Channels;

use App\Channels\EmailChannel;
use App\Mail\OtpMail;
use Illuminate\Support\Facades\Mail;
use Tests\Unit\UnitTestCase;

class EmailChannelTest extends UnitTestCase
{
    protected EmailChannel $emailChannel;

    protected function setUp(): void
    {
        parent::setUp();
        $this->emailChannel = new EmailChannel();
        Mail::fake();
    }

    public function test_send_registration_otp_sends_email(): void
    {
        $email = '<EMAIL>';
        $otp = '123456';

        $result = $this->emailChannel->sendRegistrationOtp($email, $otp);

        $this->assertTrue($result);

        Mail::assertSent(OtpMail::class, function ($mail) use ($email, $otp) {
            return $mail->hasTo($email) &&
                   $mail->mailData['otp'] === $otp &&
                   $mail->mailData['type'] === 'registration' &&
                   $mail->mailData['subject'] === 'Registration OTP';
        });
    }

    public function test_send_password_reset_otp_sends_email(): void
    {
        $email = '<EMAIL>';
        $otp = '654321';

        $result = $this->emailChannel->sendPasswordResetOtp($email, $otp);

        $this->assertTrue($result);

        Mail::assertSent(OtpMail::class, function ($mail) use ($email, $otp) {
            return $mail->hasTo($email) &&
                   $mail->mailData['otp'] === $otp &&
                   $mail->mailData['type'] === 'password_reset' &&
                   $mail->mailData['subject'] === 'Password Reset OTP';
        });
    }

    public function test_send_notification_with_custom_options(): void
    {
        $email = '<EMAIL>';
        $otp = '999999';
        $options = [
            'type' => 'custom_otp',
            'subject' => 'Custom OTP',
            'template' => 'emails.custom-otp',
        ];

        $result = $this->emailChannel->send($email, $otp, $options);

        $this->assertTrue($result);

        Mail::assertSent(OtpMail::class, function ($mail) use ($email, $otp, $options) {
            return $mail->hasTo($email) &&
                   $mail->mailData['otp'] === $otp &&
                   $mail->mailData['type'] === $options['type'] &&
                   $mail->mailData['subject'] === $options['subject'] &&
                   $mail->mailData['template'] === $options['template'];
        });
    }

    public function test_email_contains_identifier_in_metadata(): void
    {
        $email = '<EMAIL>';
        $otp = '123456';

        $this->emailChannel->sendRegistrationOtp($email, $otp);

        Mail::assertSent(OtpMail::class, function ($mail) use ($email) {
            return $mail->mailData['identifier'] === $email;
        });
    }

    public function test_email_has_resend_tags_and_metadata(): void
    {
        $email = '<EMAIL>';
        $otp = '123456';

        $this->emailChannel->sendRegistrationOtp($email, $otp);

        Mail::assertSent(OtpMail::class, function ($mail) {
            return in_array('otp', $mail->envelope()->getTags()) &&
                   in_array('registration', $mail->envelope()->getTags()) &&
                   $mail->envelope()->getMetadata()['type'] === 'registration' &&
                   $mail->envelope()->getMetadata()['identifier'] === '<EMAIL>';
        });
    }
}
