<?php

declare(strict_types=1);

namespace Tests\Unit\Channels;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class AbstractNotificationChannelTest extends TestCase
{
    protected TestNotificationChannel $channel;

    protected function setUp(): void
    {
        parent::setUp();
        $this->channel = new TestNotificationChannel;
        Cache::flush();
    }

    public function test_send_with_rate_limiting()
    {
        $destination = '<EMAIL>';

        // First send should succeed
        $result = $this->channel->send($destination, 'Test message');
        $this->assertTrue($result);

        // Check send count
        $this->assertEquals(1, $this->channel->getSendCount($destination));

        // Should still be able to send (under limit)
        $this->assertTrue($this->channel->canSend($destination));
    }

    public function test_rate_limiting_blocks_too_many_sends()
    {
        $destination = '<EMAIL>';

        // Send up to the limit
        for ($i = 0; $i < 5; $i++) {
            $this->channel->send($destination, "Test message {$i}");
        }

        // Should be blocked after limit
        $this->assertFalse($this->channel->canSend($destination));

        // Send should fail
        $result = $this->channel->send($destination, 'Blocked message');
        $this->assertFalse($result);
    }

    public function test_attempt_counting()
    {
        $destination = '<EMAIL>';

        // Initially no attempts
        $this->assertEquals(0, $this->channel->getAttemptCount($destination));
        $this->assertTrue($this->channel->canAttempt($destination));

        // Increment attempts using test method
        $this->channel->testIncrementAttempt($destination);
        $this->assertEquals(1, $this->channel->getAttemptCount($destination));

        // Should still be able to attempt
        $this->assertTrue($this->channel->canAttempt($destination));
    }

    public function test_attempt_limiting()
    {
        $destination = '<EMAIL>';

        // Increment to limit
        for ($i = 0; $i < 5; $i++) {
            $this->channel->testIncrementAttempt($destination);
        }

        // Should be blocked after limit
        $this->assertFalse($this->channel->canAttempt($destination));
    }

    public function test_send_logs_success()
    {
        Log::shouldReceive('info')
            ->once()
            ->with('Notification sent successfully', [
                'destination' => '<EMAIL>',
                'channel' => TestNotificationChannel::class,
            ]);

        $this->channel->send('<EMAIL>', 'Test message');
    }

    public function test_send_logs_failure()
    {
        $channel = new TestNotificationChannel(false); // Will fail

        Log::shouldReceive('warning')
            ->once()
            ->with('Notification send failed', [
                'destination' => '<EMAIL>',
                'channel' => TestNotificationChannel::class,
            ]);

        $result = $channel->send('<EMAIL>', 'Test message');
        $this->assertFalse($result);
    }

    public function test_send_logs_rate_limit()
    {
        $destination = '<EMAIL>';

        // Send up to limit
        for ($i = 0; $i < 5; $i++) {
            $this->channel->send($destination, "Test message {$i}");
        }

        Log::shouldReceive('info')
            ->once()
            ->with('Notification send blocked by rate limit', [
                'destination' => $destination,
                'channel' => TestNotificationChannel::class,
            ]);

        $result = $this->channel->send($destination, 'Blocked message');
        $this->assertFalse($result);
    }

    public function test_send_logs_exception()
    {
        $channel = new TestNotificationChannel(true, true); // Will throw exception

        Log::shouldReceive('error')
            ->once()
            ->with('Notification send exception', [
                'destination' => '<EMAIL>',
                'channel' => TestNotificationChannel::class,
                'error' => 'Test exception',
            ]);

        $result = $channel->send('<EMAIL>', 'Test message');
        $this->assertFalse($result);
    }
}
