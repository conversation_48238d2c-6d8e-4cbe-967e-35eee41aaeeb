<?php

declare(strict_types=1);

namespace Tests\Unit\Repositories;

use App\Models\User;
use App\Repositories\UserRepository;
use Tests\Unit\UnitTestCase;

class UserRepositoryTest extends UnitTestCase
{
    private UserRepository $userRepository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->userRepository = new UserRepository;
    }

    public function test_create_user_successfully(): void
    {
        $userData = [
            'user_type' => 'customer',
            'full_name' => '<PERSON> Do<PERSON>',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'password' => bcrypt('password123'),
            'address' => '123 Main St',
            'ward' => 'Phường 1',
            'district' => 'Quận 1',
            'city' => 'Hồ Chí Minh',
        ];

        $user = $this->userRepository->create($userData);

        $this->assertInstanceOf(User::class, $user);
        $this->assertEquals('<PERSON>', $user->full_name);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertEquals('+1234567890', $user->phone);
        $this->assertEquals('customer', $user->user_type);
        $this->assertDatabaseHas('users', [
            'full_name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'user_type' => 'customer',
        ]);
    }

    public function test_email_exists_returns_true_when_email_found(): void
    {
        User::factory()->create(['email' => '<EMAIL>']);

        $exists = $this->userRepository->emailExists('<EMAIL>');

        $this->assertTrue($exists);
    }

    public function test_email_exists_returns_false_when_email_not_found(): void
    {
        $exists = $this->userRepository->emailExists('<EMAIL>');

        $this->assertFalse($exists);
    }

    public function test_email_exists_excludes_user_id_when_provided(): void
    {
        $user = User::factory()->create(['email' => '<EMAIL>']);

        $exists = $this->userRepository->emailExists('<EMAIL>', $user->id);

        $this->assertFalse($exists);
    }

    public function test_phone_exists_returns_true_when_phone_found(): void
    {
        User::factory()->create(['phone' => '+1234567890']);

        $exists = $this->userRepository->phoneExists('+1234567890');

        $this->assertTrue($exists);
    }

    public function test_phone_exists_returns_false_when_phone_not_found(): void
    {
        $exists = $this->userRepository->phoneExists('+0987654321');

        $this->assertFalse($exists);
    }

    public function test_phone_exists_excludes_user_id_when_provided(): void
    {
        $user = User::factory()->create(['phone' => '+1234567890']);

        $exists = $this->userRepository->phoneExists('+1234567890', $user->id);

        $this->assertFalse($exists);
    }

    public function test_find_by_phone_returns_user_when_found(): void
    {
        $user = User::factory()->create(['phone' => '+1234567890']);

        $foundUser = $this->userRepository->findByPhone('+1234567890');

        $this->assertInstanceOf(User::class, $foundUser);
        $this->assertEquals($user->id, $foundUser->id);
    }

    public function test_find_by_phone_returns_null_when_not_found(): void
    {
        $foundUser = $this->userRepository->findByPhone('+0987654321');

        $this->assertNull($foundUser);
    }

    public function test_find_by_email_returns_user_when_found(): void
    {
        $user = User::factory()->create(['email' => '<EMAIL>']);

        $foundUser = $this->userRepository->findByEmail('<EMAIL>');

        $this->assertInstanceOf(User::class, $foundUser);
        $this->assertEquals($user->id, $foundUser->id);
    }

    public function test_find_by_email_returns_null_when_not_found(): void
    {
        $foundUser = $this->userRepository->findByEmail('<EMAIL>');

        $this->assertNull($foundUser);
    }

    public function test_update_user_successfully(): void
    {
        $user = User::factory()->create([
            'full_name' => 'John Doe',
            'email' => '<EMAIL>',
        ]);

        $updateData = [
            'full_name' => 'Updated Name',
            'email' => '<EMAIL>',
        ];

        $updatedUser = $this->userRepository->updateUser($user, $updateData);

        $this->assertInstanceOf(User::class, $updatedUser);
        $this->assertEquals('Updated Name', $updatedUser->full_name);
        $this->assertEquals('<EMAIL>', $updatedUser->email);
        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'full_name' => 'Updated Name',
            'email' => '<EMAIL>',
        ]);
    }

    public function test_delete_user_successfully(): void
    {
        $user = User::factory()->create();

        $this->userRepository->delete($user->id);

        $this->assertDatabaseMissing('users', [
            'id' => $user->id,
        ]);
    }
}
