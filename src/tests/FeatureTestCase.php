<?php

declare(strict_types=1);

namespace Tests;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

abstract class FeatureTestCase extends TestCase
{
    use RefreshDatabase;

    public User $user;

    protected string $token;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create([
            'password' => Hash::make('password123'),
        ]);
    }

    protected function getAuthHeaders(): array
    {
        return [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $this->token,
        ];
    }

    protected function getHeaders(): array
    {
        return [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ];
    }

    protected function authenticateUser(): void
    {
        // Since /login now requires OTP, we need to mock the OTP service
        // or create a user with phone and use OTP authentication
        // For testing purposes, we'll create the token directly
        $tokenResult = $this->user->createToken('test-token');
        $this->token = $tokenResult->plainTextToken;
    }

    protected function createAndAuthenticateUser(array $attributes = []): User
    {
        $user = User::factory()->create(array_merge([
            'password' => Hash::make('password123'),
        ], $attributes));

        // Create token directly for testing
        $tokenResult = $user->createToken('test-token');
        $this->token = $tokenResult->plainTextToken;
        $this->user = $user;

        return $user;
    }
}
