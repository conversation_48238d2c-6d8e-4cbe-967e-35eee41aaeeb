# Testing & Quality Assurance

## Test Structure

### Directory Organization
```
src/tests/
├── TestCase.php                    # Base test case with RefreshDatabase
├── Feature/
│   ├── FeatureTestCase.php         # Base class for feature tests
│   ├── ExampleTest.php
│   └── AuthControllerTest.php      # API endpoint tests
└── Unit/
    ├── UnitTestCase.php            # Base class for unit tests
    ├── ExampleTest.php
    ├── Controllers/                # Controller logic tests
    │   └── AuthControllerTest.php
    ├── Services/                   # Business logic tests
    │   └── AuthServiceTest.php
    ├── Repositories/               # Data access tests
    │   └── UserRepositoryTest.php
    └── Helpers/                    # Utility function tests
        ├── CommonHelperTest.php
        └── CacheHelperTest.php
```

### Base Test Classes

#### TestCase.php
- Base class for all tests
- Uses `RefreshDatabase` trait for clean database state
- Disables Telescope in tests
- Configured for in-memory SQLite database

#### FeatureTestCase.php
- Extends `TestCase` for API/feature tests
- Uses `DatabaseTransactions` for fast rollback
- Provides authentication helpers
- Includes common API headers and methods

#### UnitTestCase.php
- Extends `TestCase` for unit tests
- Uses `RefreshDatabase` for isolated testing
- Provides additional setup for unit tests

## Testing Standards

### Testing Tools
- Use Laravel's built-in PHPUnit testing framework
- Use `php artisan test` command for running tests
- Configure `phpunit.xml` with proper test suites
- Use in-memory SQLite database for fast testing

### Test Organization
- **Feature Tests**: Test complete API endpoints and user workflows
- **Unit Tests**: Test individual components in isolation
- **Controllers**: Test request validation, service calls, and response handling
- **Services**: Test business logic with mocked dependencies
- **Repositories**: Test data access with real database
- **Helpers**: Test utility functions and pure methods

### Test Naming Conventions
- Use descriptive test method names: `test_user_can_register_with_valid_data()`
- Group related tests in test classes by component
- Follow PSR-2 coding standards
- Use `declare(strict_types=1);` in all test files

## Unit Testing

### Service Testing
- Test all business logic in services
- Mock external dependencies using Laravel's `$this->mock()`
- Test edge cases and error conditions
- Ensure proper exception handling
- Use `shouldReceive()`, `andReturn()`, `shouldNotReceive()` for mocking

```php
// Example service test
$this->userRepository
    ->shouldReceive('emailExists')
    ->once()
    ->with('<EMAIL>')
    ->andReturn(false);
```

### Helper Testing
- Test utility functions and helpers
- Verify helper method behavior with various inputs
- Test helper method edge cases
- Ensure helper methods are pure functions
- Test both success and failure scenarios

### Repository Testing
- Test data access methods
- Use real database with `RefreshDatabase`
- Test CRUD operations
- Verify query results and database state
- Use factories for test data

### Controller Testing
- Test request validation
- Mock service dependencies
- Test response formats and status codes
- Verify error handling
- Test authentication and authorization

## Feature Testing

### API Testing
- Test complete API endpoints
- Use `FeatureTestCase` for authentication helpers
- Verify request validation and error responses
- Test response JSON structure
- Use `postJson()`, `getJson()`, `putJson()` methods

```php
// Example API test
$response = $this->postJson('/api/v1/auth/register', $userData);
$response->assertStatus(201)
    ->assertJsonStructure([
        'success',
        'message',
        'data' => ['user', 'access_token', 'token_type']
    ]);
```

### Database Testing
- Use `DatabaseTransactions` for feature tests
- Use `RefreshDatabase` for unit tests
- Test database relationships and constraints
- Verify data persistence and retrieval
- Use `assertDatabaseHas()` and `assertDatabaseMissing()`

### Authentication Testing
- Test authentication flows (login, register, logout)
- Test protected routes with authentication
- Verify token generation and validation
- Test authorization rules and permissions
- Use `authenticateUser()` helper method

## Test Quality

### Test Coverage
- Aim for high test coverage on critical business logic
- Focus on testing both success and failure scenarios
- Include integration tests for complex workflows
- Test validation rules and error conditions

### Test Data Management
- Use Laravel factories for test data generation
- Create realistic test scenarios
- Use database seeders for complex test data
- Clean up test data using database transactions
- Use `User::factory()->create()` for model creation

### Performance Considerations
- Use in-memory SQLite for fast test execution
- Mock external services to avoid network calls
- Use database transactions for fast rollback
- Avoid unnecessary database queries in tests

## Code Quality

### Static Analysis
- Use PHPStan for static analysis
- Follow established code quality standards
- Use the established linting and formatting rules
- Ensure all code passes the project's quality checks

### Test Best Practices
- Write tests that are easy to understand and maintain
- Use descriptive test names that explain the scenario
- Group related tests in test classes
- Follow the established test structure
- Use proper assertions and error messages

### Security Testing
- Test authentication and authorization
- Verify input validation and sanitization
- Test for common security vulnerabilities
- Ensure proper error handling without information leakage

## Running Tests

### Commands
```bash
# Run all tests
php artisan test

# Run specific test suites
php artisan test --testsuite=Unit
php artisan test --testsuite=Feature

# Run specific test folders
php artisan test tests/Unit/Services/
php artisan test tests/Unit/Controllers/

# Run with coverage
php artisan test --coverage

# Run specific test file
php artisan test tests/Feature/AuthControllerTest.php
```

### Configuration
- Tests use in-memory SQLite database
- Telescope is disabled in tests
- Cache and session use array drivers
- Mail uses array driver for testing
- Queue uses sync driver for immediate execution

## Test Examples

### Feature Test Example
```php
<?php

declare(strict_types=1);

namespace Tests\Feature;

use Tests\Feature\FeatureTestCase;

class AuthControllerTest extends FeatureTestCase
{
    public function test_user_can_register_with_valid_data(): void
    {
        $userData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => ['user', 'access_token', 'token_type']
            ]);
    }
}
```

### Unit Test Example
```php
<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use Tests\Unit\UnitTestCase;

class AuthServiceTest extends UnitTestCase
{
    public function test_register_creates_user_successfully(): void
    {
        $this->userRepository
            ->shouldReceive('emailExists')
            ->once()
            ->with('<EMAIL>')
            ->andReturn(false);

        $result = $this->authService->register($userData);

        $this->assertArrayHasKey('user', $result);
        $this->assertArrayHasKey('access_token', $result);
    }
}
```

This testing structure ensures comprehensive coverage, maintainable tests, and follows Laravel best practices for testing. 