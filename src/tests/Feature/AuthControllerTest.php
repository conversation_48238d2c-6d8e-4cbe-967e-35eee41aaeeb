<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Tests\FeatureTestCase;

class AuthControllerTest extends FeatureTestCase
{
    public function test_user_can_login_with_valid_credentials(): void
    {
        // Create a user with email and password
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'Test@123',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'user' => [
                        'id',
                        'email',
                        'first_name',
                        'last_name',
                        'phone_number',
                    ],
                    'access_token',
                    'refresh_token',
                    'token_type',
                ],
            ]);
    }

    public function test_user_cannot_login_with_invalid_credentials(): void
    {
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword',
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'detail',
            ]);
    }

    public function test_user_cannot_login_with_inactive_account(): void
    {
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'inactive',
        ]);

        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'Test@123',
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'detail',
            ]);
    }

    public function test_user_can_get_profile_when_authenticated(): void
    {
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/v1/auth/profile');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'user' => [
                        'id',
                        'email',
                        'first_name',
                        'last_name',
                        'phone_number',
                    ],
                ],
            ]);
    }

    public function test_user_cannot_get_profile_when_not_authenticated(): void
    {
        $response = $this->getJson('/api/v1/auth/profile');

        $response->assertStatus(401);
    }

    public function test_user_can_update_profile_when_authenticated(): void
    {
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        $updateData = [
            'first_name' => 'Updated',
            'last_name' => 'Name',
            'phone_number' => '+1234567890',
        ];

        $response = $this->actingAs($this->user)
            ->putJson('/api/v1/auth/profile', $updateData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'user' => [
                        'id',
                        'email',
                        'first_name',
                        'last_name',
                        'phone_number',
                    ],
                ],
            ]);
    }

    public function test_user_can_logout_when_authenticated(): void
    {
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/auth/logout');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
            ]);
    }

    public function test_user_can_initiate_forgot_password_with_registered_email(): void
    {
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        $response = $this->postJson('/api/v1/auth/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'email',
                    'expires_in',
                    'remaining_attempts',
                ],
            ]);
    }

    public function test_user_cannot_initiate_forgot_password_with_unregistered_email(): void
    {
        $response = $this->postJson('/api/v1/auth/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'detail',
            ]);
    }

    public function test_user_cannot_initiate_forgot_password_with_invalid_email(): void
    {
        $response = $this->postJson('/api/v1/auth/forgot-password', [
            'email' => 'invalid-email',
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'detail',
            ]);
    }

    public function test_user_cannot_initiate_forgot_password_with_deactivated_account(): void
    {
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'inactive',
        ]);

        $response = $this->postJson('/api/v1/auth/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'detail',
            ]);
    }

    public function test_user_can_verify_reset_otp_with_valid_data(): void
    {
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        $response = $this->postJson('/api/v1/auth/verify-reset-otp', [
            'email' => '<EMAIL>',
            'otp' => '123456',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'email',
                    'verified',
                    'reset_token',
                    'reset_token_expires_in',
                ],
            ]);
    }

    public function test_user_cannot_verify_reset_otp_with_invalid_otp(): void
    {
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        $response = $this->postJson('/api/v1/auth/verify-reset-otp', [
            'email' => '<EMAIL>',
            'otp' => '000000',
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'detail',
            ]);
    }

    public function test_user_cannot_verify_reset_otp_with_invalid_email_format(): void
    {
        $response = $this->postJson('/api/v1/auth/verify-reset-otp', [
            'email' => 'invalid-email',
            'otp' => '123456',
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'detail',
            ]);
    }

    public function test_user_can_reset_password_with_valid_token(): void
    {
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        $response = $this->postJson('/api/v1/auth/reset-password', [
            'email' => '<EMAIL>',
            'password' => 'NewPassword123!',
            'password_confirmation' => 'NewPassword123!',
            'token' => 'valid_token_123',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'success',
                ],
            ]);
    }

    public function test_user_cannot_reset_password_with_invalid_token(): void
    {
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        $response = $this->postJson('/api/v1/auth/reset-password', [
            'email' => '<EMAIL>',
            'password' => 'NewPassword123!',
            'password_confirmation' => 'NewPassword123!',
            'token' => 'invalid_token',
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'detail',
            ]);
    }

    public function test_user_cannot_reset_password_with_invalid_password(): void
    {
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        $response = $this->postJson('/api/v1/auth/reset-password', [
            'email' => '<EMAIL>',
            'password' => 'weak',
            'password_confirmation' => 'weak',
            'token' => 'valid_token_123',
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'detail',
            ]);
    }

    public function test_user_cannot_reset_password_with_mismatched_confirmation(): void
    {
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        $response = $this->postJson('/api/v1/auth/reset-password', [
            'email' => '<EMAIL>',
            'password' => 'NewPassword123!',
            'password_confirmation' => 'DifferentPassword123!',
            'token' => 'valid_token_123',
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'detail',
            ]);
    }

    public function test_user_cannot_reset_password_with_invalid_email(): void
    {
        $response = $this->postJson('/api/v1/auth/reset-password', [
            'email' => 'invalid-email',
            'password' => 'NewPassword123!',
            'password_confirmation' => 'NewPassword123!',
            'token' => 'valid_token_123',
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'detail',
            ]);
    }

    public function test_user_can_refresh_token(): void
    {
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        $response = $this->postJson('/api/v1/auth/refresh-token', [
            'refresh_token' => 'valid_refresh_token_123',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'access_token',
                    'refresh_token',
                    'user',
                    'token_type',
                ],
            ]);
    }

    public function test_user_can_update_password_when_authenticated(): void
    {
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('OldPassword123!'),
            'status' => 'active',
        ]);

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/auth/update-password', [
                'current_password' => 'OldPassword123!',
                'password' => 'NewPassword123!',
                'password_confirmation' => 'NewPassword123!',
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'success',
                ],
            ]);
    }

    public function test_user_cannot_update_password_with_wrong_current_password(): void
    {
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('OldPassword123!'),
            'status' => 'active',
        ]);

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/auth/update-password', [
                'current_password' => 'WrongPassword123!',
                'password' => 'NewPassword123!',
                'password_confirmation' => 'NewPassword123!',
            ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'detail',
            ]);
    }
}
