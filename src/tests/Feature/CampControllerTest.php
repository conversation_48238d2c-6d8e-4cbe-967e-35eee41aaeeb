<?php

namespace Tests\Feature;

use App\Models\Camp;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CampControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_index_returns_paginated_camps(): void
    {
        $email = fake()->unique()->safeEmail();
        User::factory()->create(['email' => $email]);
        $user = User::where('email', $email)->firstOrFail();

        $this->actingAs($user);

        Camp::factory()->count(3)->create([
            'created_by' => $user->id,
            'updated_by' => $user->id,
        ]);

        $response = $this->getJson('/api/v1/camps');

        $response->assertOk()
            ->assertJsonStructure([
                'data' => [
                    '*' => ['id', 'name', 'description', 'start_date', 'end_date', 'location', 'max_capacity', 'price', 'status'],
                ],
            ]);
    }

    public function test_index_fails_when_not_authenticated(): void
    {
        $response = $this->getJson('/api/v1/camps');
        $response->assertUnauthorized();
    }

    public function test_store_creates_camp(): void
    {
        $email = fake()->unique()->safeEmail();
        User::factory()->create(['email' => $email]);
        $user = User::where('email', $email)->firstOrFail();

        $this->actingAs($user);

        $payload = [
            'name' => 'Test Camp',
            'description' => 'A great test camp',
            'location' => 'Ho Chi Minh City',
            'start_date' => now()->addDays(1)->format('Y-m-d'),
            'end_date' => now()->addDays(3)->format('Y-m-d'),
            'max_capacity' => 40,
            'price' => 500,
            'status' => 'active',
        ];

        $response = $this->postJson('/api/v1/camps', $payload);

        $response->assertCreated();

        $this->assertDatabaseHas('camps', [
            'name' => 'Test Camp',
            'created_by' => $user->id,
            'updated_by' => $user->id,
        ]);
    }

    public function test_store_fails_with_invalid_data(): void
    {
        $email = fake()->unique()->safeEmail();
        User::factory()->create(['email' => $email]);
        $user = User::where('email', $email)->firstOrFail();

        $this->actingAs($user);

        $payload = [
            'description' => 'Missing required fields',
        ];

        $response = $this->postJson('/api/v1/camps', $payload);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name', 'location', 'start_date', 'end_date']);
    }

    public function test_show_returns_camp_detail(): void
    {
        $email = fake()->unique()->safeEmail();
        User::factory()->create(['email' => $email]);
        $user = User::where('email', $email)->firstOrFail();

        $camp = Camp::factory()->create([
            'created_by' => $user->id,
            'updated_by' => $user->id,
        ]);

        $this->actingAs($user);

        $response = $this->getJson("/api/v1/camps/{$camp->id}");

        $response->assertOk()
            ->assertJson([
                'data' => [
                    'id' => $camp->id,
                    'name' => $camp->name,
                ],
            ]);
    }

    public function test_show_returns_404_for_invalid_id(): void
    {
        $email = fake()->unique()->safeEmail();
        User::factory()->create(['email' => $email]);
        $user = User::where('email', $email)->firstOrFail();

        $this->actingAs($user);

        $response = $this->getJson('/api/v1/camps/999999');

        $response->assertNotFound();
    }

    public function test_update_edits_existing_camp(): void
    {
        $email = fake()->unique()->safeEmail();
        User::factory()->create(['email' => $email]);
        $user = User::where('email', $email)->firstOrFail();

        $this->actingAs($user);

        $camp = Camp::factory()->create([
            'created_by' => $user->id,
            'updated_by' => $user->id,
        ]);

        $updateData = [
            'name' => 'Updated Camp Name',
            'description' => $camp->description,
            'location' => $camp->location,
            'start_date' => $camp->start_date,
            'end_date' => $camp->end_date,
            'max_capacity' => $camp->max_capacity,
            'price' => $camp->price,
            'status' => $camp->status,
        ];

        $response = $this->putJson("/api/v1/camps/{$camp->id}", $updateData);

        $response->assertOk();

        $this->assertDatabaseHas('camps', [
            'id' => $camp->id,
            'name' => 'Updated Camp Name',
            'updated_by' => $user->id,
        ]);
    }

    public function test_update_fails_with_invalid_id(): void
    {
        $email = fake()->unique()->safeEmail();
        User::factory()->create(['email' => $email]);
        $user = User::where('email', $email)->firstOrFail();

        $this->actingAs($user);

        $updateData = [
            'name' => 'Invalid Update',
            'description' => 'test',
            'location' => 'Unknown',
            'start_date' => now()->addDay()->format('Y-m-d'),
            'end_date' => now()->addDays(2)->format('Y-m-d'),
            'max_capacity' => 10,
            'price' => 100,
            'status' => 'active',
        ];

        $response = $this->putJson('/api/v1/camps/999999', $updateData);

        $response->assertNotFound();
    }

    public function test_destroy_deletes_camp(): void
    {
        $email = fake()->unique()->safeEmail();
        User::factory()->create(['email' => $email]);
        $user = User::where('email', $email)->firstOrFail();

        $this->actingAs($user);

        $camp = Camp::factory()->create([
            'created_by' => $user->id,
            'updated_by' => $user->id,
        ]);

        $response = $this->deleteJson("/api/v1/camps/{$camp->id}");

        $response->assertOk();

        $this->assertDatabaseMissing('camps', [
            'id' => $camp->id,
        ]);
    }

    public function test_destroy_fails_when_not_authenticated(): void
    {
        $camp = Camp::factory()->create();

        $response = $this->deleteJson("/api/v1/camps/{$camp->id}");

        $response->assertUnauthorized();
    }
}
