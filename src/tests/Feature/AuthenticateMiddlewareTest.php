<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AuthenticateMiddlewareTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('db:seed', ['--class' => 'RoleSeeder']);
    }

    public function test_unauthenticated_user_redirected_to_login_for_admin_routes(): void
    {
        $response = $this->get('/admin/dashboard');

        $response->assertStatus(302)
            ->assertRedirect('/login')
            ->assertSessionHas('error', __('messages.portal.auth.login_required'));
    }

    public function test_unauthenticated_user_redirected_to_login_for_retailer_routes(): void
    {
        $response = $this->get('/retailer/dashboard');

        $response->assertStatus(302)
            ->assertRedirect('/login')
            ->assertSessionHas('error', __('messages.portal.auth.login_required'));
    }

    public function test_inactive_user_logged_out_and_redirected(): void
    {
        $admin = User::factory()->admin()->inactive()->create();
        $admin->assignRole('admin');

        $this->actingAs($admin);

        $response = $this->get('/admin/dashboard');

        $response->assertStatus(302)
            ->assertRedirect('/login')
            ->assertSessionHas('error', __('messages.portal.auth.account_deactivated'));
    }

    public function test_logged_in_admin_redirected_from_login_page(): void
    {
        $admin = User::factory()->admin()->create();
        $admin->assignRole('admin');

        $this->actingAs($admin);

        $response = $this->get('/login');

        $response->assertStatus(302)
            ->assertRedirect('/admin/dashboard')
            ->assertSessionHas('info', __('messages.portal.auth.already_logged_in'));
    }

    public function test_logged_in_retailer_redirected_from_login_page(): void
    {
        $retailer = User::factory()->retailer()->create();
        $retailer->assignRole('retailer');

        $this->actingAs($retailer);

        $response = $this->get('/login');

        $response->assertStatus(302)
            ->assertRedirect('/retailer/dashboard')
            ->assertSessionHas('info', __('messages.portal.auth.already_logged_in'));
    }

    public function test_logged_in_customer_redirected_from_login_page(): void
    {
        $customer = User::factory()->customer()->create();

        $this->actingAs($customer);

        $response = $this->get('/login');

        $response->assertStatus(302)
            ->assertRedirect('/')
            ->assertSessionHas('info', __('messages.portal.auth.already_logged_in'));
    }

    public function test_inactive_user_redirected_from_login_page(): void
    {
        $admin = User::factory()->admin()->inactive()->create();
        $admin->assignRole('admin');

        $this->actingAs($admin);

        $response = $this->get('/login');

        $response->assertStatus(302)
            ->assertRedirect('/login')
            ->assertSessionHas('error', __('messages.portal.auth.account_deactivated'));
    }
}
