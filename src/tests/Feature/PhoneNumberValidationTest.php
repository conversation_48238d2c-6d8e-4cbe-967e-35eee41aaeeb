<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\FeatureTestCase;

class PhoneNumberValidationTest extends FeatureTestCase
{
    use RefreshDatabase;

    public User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'status' => 'active',
        ]);
    }

    public function test_accepts_french_phone_numbers_in_various_formats()
    {
        $validPhoneNumbers = [
            '+33123456789',
            '+33 1 23 45 67 89',
            '+33-1-23-45-67-89',
            '+33 (1) 23 45 67 89',
            '+33 1 23 45 67 89',
        ];

        foreach ($validPhoneNumbers as $phoneNumber) {
            $response = $this->actingAs($this->user)
                ->postJson('/api/v1/auth/update-profile', [
                    'phone_number' => $phoneNumber,
                ]);

            $response->assertStatus(200);
        }
    }

    public function test_accepts_international_phone_numbers()
    {
        $internationalNumbers = [
            '+1234567890',
            '+44 20 7946 0958',
            '+49 30 12345678',
        ];

        foreach ($internationalNumbers as $phoneNumber) {
            $response = $this->actingAs($this->user)
                ->postJson('/api/v1/auth/update-profile', [
                    'phone_number' => $phoneNumber,
                ]);

            // Note: This might fail if the libphonenumber service is configured to only accept French numbers
            // The test documents the expected behavior
            $response->assertStatus(200);
        }
    }

    public function test_rejects_invalid_phone_number_formats()
    {
        $invalidPhoneNumbers = [
            'not-a-number',
            '123',
            'abc123def',
            '++33123456789',
            '+33 123 456 789 012 345', // Too long
        ];

        foreach ($invalidPhoneNumbers as $phoneNumber) {
            $response = $this->actingAs($this->user)
                ->postJson('/api/v1/auth/update-profile', [
                    'phone_number' => $phoneNumber,
                ]);

            $response->assertStatus(422);
        }
    }

    public function test_accepts_empty_phone_number()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/auth/update-profile', [
                'phone_number' => '',
            ]);

        $response->assertStatus(200);
    }

    public function test_accepts_null_phone_number()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/auth/update-profile', [
                'phone_number' => null,
            ]);

        $response->assertStatus(200);
    }
}
