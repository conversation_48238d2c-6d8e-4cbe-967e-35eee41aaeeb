<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\FeatureTestCase;

class AuthenticationValidationTest extends FeatureTestCase
{
    use RefreshDatabase;

    public function test_login_validation_returns_message_codes()
    {
        $response = $this->postJson('/api/v1/auth/login', [
            'email' => 'invalid-email',
            'password' => '',
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'errors',
            ])
            ->assertJson([
                'success' => false,
                'message' => 'validation_failed',
            ]);

        // Check that errors contain message codes, not full messages
        $errors = $response->json('errors');
        $this->assertArrayHasKey('email', $errors);
        $this->assertArrayHasKey('password', $errors);
        
        // The error messages should be message codes, not full translated messages
        $this->assertContains('email', $errors['email']);
        $this->assertContains('required', $errors['password']);
    }

    public function test_login_with_nonexistent_user_returns_message_code()
    {
        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'validation_failed',
            ]);

        $errors = $response->json('errors');
        $this->assertArrayHasKey('email', $errors);
        $this->assertContains('account_not_found', $errors['email']);
    }

    public function test_login_with_incorrect_password_returns_message_code()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('correctpassword'),
            'status' => 'active',
        ]);

        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword',
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'validation_failed',
            ]);

        $errors = $response->json('errors');
        $this->assertArrayHasKey('password', $errors);
        $this->assertContains('incorrect_credentials', $errors['password']);
    }

    public function test_login_with_inactive_user_returns_message_code()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'status' => 'inactive',
        ]);

        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'validation_failed',
            ]);

        $errors = $response->json('errors');
        $this->assertArrayHasKey('email', $errors);
        $this->assertContains('account_deactivated_contact_support', $errors['email']);
    }

    public function test_otp_validation_returns_message_codes()
    {
        $response = $this->postJson('/api/v1/auth/forgot-password', [
            'email' => 'invalid-email',
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'validation_failed',
            ]);

        $errors = $response->json('errors');
        $this->assertArrayHasKey('email', $errors);
        $this->assertContains('email', $errors['email']);
    }

    public function test_refresh_token_validation_returns_message_codes()
    {
        $response = $this->postJson('/api/v1/auth/refresh-token', [
            'refresh_token' => '',
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'validation_failed',
            ]);

        $errors = $response->json('errors');
        $this->assertArrayHasKey('refresh_token', $errors);
        $this->assertContains('required', $errors['refresh_token']);
    }

    public function test_update_profile_validation_returns_message_codes()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'status' => 'active',
        ]);

        $response = $this->actingAs($user)
            ->postJson('/api/v1/auth/update-profile', [
                'email' => 'invalid-email',
                'phone_number' => 'invalid-phone',
            ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'validation_failed',
            ]);

        $errors = $response->json('errors');
        $this->assertArrayHasKey('email', $errors);
        $this->assertArrayHasKey('phone_number', $errors);
        $this->assertContains('email', $errors['email']);
    }
}
