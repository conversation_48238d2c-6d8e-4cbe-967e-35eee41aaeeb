<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RoleBasedAccessTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('db:seed', ['--class' => 'RoleSeeder']);
    }

    public function test_admin_can_access_all_routes(): void
    {
        $admin = User::factory()->admin()->create();
        $admin->assignRole('admin');

        $this->actingAs($admin);

        // Test shared routes (accessible by both admin and retailer)
        $this->get('/admin/dashboard')->assertStatus(200);
        $this->get('/admin/products')->assertStatus(200);
        $this->get('/admin/rewards')->assertStatus(200);
        $this->get('/admin/promotions')->assertStatus(200);
        $this->get('/admin/vouchers')->assertStatus(200);
        $this->get('/change-password')->assertStatus(302);
        $this->get('/verify-phone')->assertStatus(302);

        // Test admin-only routes
        $this->get('/admin/products/import-export')->assertStatus(200);
        $this->get('/admin/rewards/create')->assertStatus(200);
        $this->get('/admin/promotions/create')->assertStatus(200);
        $this->get('/admin/vouchers/create')->assertStatus(200);
        $this->get('/admin/retailers')->assertStatus(200);
    }

    public function test_retailer_can_access_shared_routes(): void
    {
        $retailer = User::factory()->retailer()->create();
        $retailer->assignRole('retailer');

        $this->actingAs($retailer);

        // Test shared routes (accessible by both admin and retailer)
        // Retailer can access their dashboard
        $this->get('/retailer/dashboard')->assertStatus(200);
        $this->get('/change-password')->assertStatus(302);
        $this->get('/verify-phone')->assertStatus(302);
    }

    public function test_retailer_cannot_access_admin_only_routes(): void
    {
        $retailer = User::factory()->retailer()->create();
        $retailer->assignRole('retailer');

        $this->actingAs($retailer);

        // Test admin-only routes (should be forbidden for retailer)
        $this->get('/admin/products/import-export')->assertStatus(403);
        $this->get('/admin/rewards/create')->assertStatus(403);
        $this->get('/admin/promotions/create')->assertStatus(403);
        $this->get('/admin/vouchers/create')->assertStatus(403);
        $this->get('/admin/retailers')->assertStatus(403);
    }

    public function test_customer_cannot_access_admin_routes(): void
    {
        $customer = User::factory()->customer()->create();

        $this->actingAs($customer);

        // Test that customers cannot access any admin routes
        $this->get('/admin/dashboard')->assertStatus(403);
        $this->get('/admin/products')->assertStatus(403);
        $this->get('/admin/rewards')->assertStatus(403);
        $this->get('/admin/promotions')->assertStatus(403);
        $this->get('/admin/vouchers')->assertStatus(403);
        $this->get('/change-password')->assertStatus(302);
        $this->get('/verify-phone')->assertStatus(302);
    }

    public function test_unauthenticated_user_cannot_access_admin_routes(): void
    {
        // Test that unauthenticated users are redirected to login
        $this->get('/admin/dashboard')->assertStatus(302);
        $this->get('/admin/products')->assertStatus(302);
        $this->get('/admin/rewards')->assertStatus(302);
        $this->get('/admin/promotions')->assertStatus(302);
        $this->get('/admin/vouchers')->assertStatus(302);
        $this->get('/change-password')->assertStatus(302);
        $this->get('/verify-phone')->assertStatus(302);
    }
}
