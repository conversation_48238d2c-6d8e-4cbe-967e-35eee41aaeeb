<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class ForgotPasswordTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();

        // Clear cache to avoid rate limiting between tests
        \Illuminate\Support\Facades\Cache::flush();
    }

    public function test_forgot_password_request_form_can_be_displayed(): void
    {
        $response = $this->get('/forgot-password');

        $response->assertStatus(200)
            ->assertViewIs('forgot-password.request');
    }

    public function test_can_send_otp_for_forgot_password(): void
    {
        $user = User::factory()->customer()->create([
            'phone' => '+84901234567',
            'phone_verified_at' => now(),
        ]);

        $response = $this->post('/forgot-password', [
            'phone' => '+84901234567',
        ]);

        $response->assertStatus(302)
            ->assertRedirect('/forgot-password/otp')
            ->assertSessionHas('success');
    }

    public function test_cannot_send_otp_for_unverified_phone(): void
    {
        $user = User::factory()->customer()->create([
            'phone' => '+84901234567',
            'phone_verified_at' => null,
        ]);

        $response = $this->post('/forgot-password', [
            'phone' => '+84901234567',
        ]);

        $response->assertStatus(302)
            ->assertRedirect()
            ->assertSessionHas('error', 'Phone number is not verified.');
    }

    public function test_cannot_send_otp_for_nonexistent_phone(): void
    {
        $response = $this->post('/forgot-password', [
            'phone' => '+84987654321',
        ]);

        $response->assertStatus(302)
            ->assertRedirect()
            ->assertSessionHas('error', 'Phone number not found.');
    }

    public function test_cannot_access_otp_form_without_phone_session(): void
    {
        $response = $this->get('/forgot-password/otp');

        $response->assertStatus(302)
            ->assertRedirect('/forgot-password')
            ->assertSessionHas('error', 'Please enter your phone number first.');
    }

    public function test_can_access_otp_form_with_phone_session(): void
    {
        session(['forgot_password_phone' => '+84901234567']);

        $response = $this->get('/forgot-password/otp');

        $response->assertStatus(200)
            ->assertViewIs('forgot-password.otp');
    }

    public function test_cannot_access_reset_form_without_verification(): void
    {
        session(['forgot_password_phone' => '+84901234567']);

        $response = $this->get('/forgot-password/reset');

        $response->assertStatus(302)
            ->assertRedirect('/forgot-password')
            ->assertSessionHas('error', 'Unauthorized access to password reset.');
    }

    public function test_can_access_reset_form_with_verification(): void
    {
        session([
            'forgot_password_phone' => '+84901234567',
            'forgot_password_otp_verified' => true,
        ]);

        $response = $this->get('/forgot-password/reset');

        $response->assertStatus(200)
            ->assertViewIs('forgot-password.reset');
    }

    public function test_can_reset_password_successfully(): void
    {
        $user = User::factory()->customer()->create([
            'phone' => '+84901234567',
            'phone_verified_at' => now(),
        ]);

        session([
            'forgot_password_phone' => '+84901234567',
            'forgot_password_otp_verified' => true,
        ]);

        $response = $this->post('/forgot-password/reset', [
            'password' => 'NewPassword123!',
            'password_confirmation' => 'NewPassword123!',
        ]);

        $response->assertStatus(302)
            ->assertRedirect('/login')
            ->assertSessionHas('success');
    }

    public function test_otp_displayed_in_development_environment(): void
    {
        // Mock the environment to be development
        $this->app['config']->set('app.env', 'local');

        $user = User::factory()->customer()->create([
            'phone' => '+84901234567',
            'phone_verified_at' => now(),
        ]);

        // Clear any existing cache
        \Illuminate\Support\Facades\Cache::flush();

        // Send OTP first
        $response = $this->post('/forgot-password', [
            'phone' => '+84901234567',
        ]);

        // Verify OTP was sent and session was set
        $response->assertStatus(302)
            ->assertRedirect('/forgot-password/otp')
            ->assertSessionHas('success');

        // Access OTP form
        $response = $this->get('/forgot-password/otp');

        $response->assertStatus(200)
            ->assertViewIs('forgot-password.otp')
            ->assertSee('Development Mode')
            ->assertSee('Your OTP is:');
    }

    public function test_can_resend_otp_from_otp_form(): void
    {
        $user = User::factory()->customer()->create([
            'phone' => '+84901234567',
            'phone_verified_at' => now(),
        ]);

        // Clear any existing cache
        \Illuminate\Support\Facades\Cache::flush();

        // First send OTP
        $this->post('/forgot-password', [
            'phone' => '+84901234567',
        ]);

        // Verify we can access OTP form
        $this->get('/forgot-password/otp')->assertStatus(200);

        // Clear cache again before resend
        \Illuminate\Support\Facades\Cache::flush();

        // Resend OTP from OTP form
        $response = $this->post('/forgot-password', [
            'resend' => '1',
            'phone' => '+84901234567',
        ]);

        $response->assertStatus(302)
            ->assertRedirect('/forgot-password/otp')
            ->assertSessionHas('success', __('messages.forgot_password.otp_resent'));
    }

    public function test_resend_otp_shows_rate_limit_error(): void
    {
        $user = User::factory()->customer()->create([
            'phone' => '+84901234567',
            'phone_verified_at' => now(),
        ]);
        \Illuminate\Support\Facades\Cache::flush();
        // Gửi OTP 3 lần để đạt giới hạn
        for ($i = 0; $i < 3; $i++) {
            $this->post('/forgot-password', [
                'phone' => '+84901234567',
            ]);
        }
        // Lần thứ 4 sẽ bị rate limit
        $response = $this->post('/forgot-password', [
            'resend' => '1',
            'phone' => '+84901234567',
        ]);
        $response->assertStatus(302)
            ->assertRedirect('/forgot-password/otp')
            ->assertSessionHas('error', 'Too many OTP requests. Please try again later');
    }

    public function test_cannot_reset_password_without_verification(): void
    {
        session(['forgot_password_phone' => '+84901234567']);

        $response = $this->post('/forgot-password/reset', [
            'password' => 'NewPassword123!',
            'password_confirmation' => 'NewPassword123!',
        ]);

        $response->assertStatus(302)
            ->assertRedirect('/forgot-password')
            ->assertSessionHas('error', 'Unauthorized access to password reset.');
    }

    public function test_cannot_reset_password_with_weak_password(): void
    {
        session([
            'forgot_password_phone' => '+84901234567',
            'forgot_password_otp_verified' => true,
        ]);

        $response = $this->post('/forgot-password/reset', [
            'password' => 'weak',
            'password_confirmation' => 'weak',
        ]);

        $response->assertStatus(302)
            ->assertRedirect()
            ->assertSessionHasErrors(['password']);
    }

    public function test_cannot_reset_password_with_mismatched_confirmation(): void
    {
        session([
            'forgot_password_phone' => '+84901234567',
            'forgot_password_otp_verified' => true,
        ]);

        $response = $this->post('/forgot-password/reset', [
            'password' => 'NewPassword123!',
            'password_confirmation' => 'DifferentPassword123!',
        ]);

        $response->assertStatus(302)
            ->assertRedirect()
            ->assertSessionHasErrors(['password']);
    }
}
