<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\FeatureTestCase;

class UpdateProfileTest extends FeatureTestCase
{
    use RefreshDatabase;

    public User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'first_name' => 'John',
            'last_name' => 'Doe',
            'status' => 'active',
        ]);
    }

    public function test_can_update_first_name()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/auth/update-profile', [
                'first_name' => 'Jane',
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'user_profile_updated_successfully',
            ])
            ->assertJson([
                'data' => [
                    'user' => [
                        'first_name' => 'Jane',
                    ],
                ],
            ]);

        $this->user->refresh();
        $this->assertEquals('Jane', $this->user->first_name);
    }

    public function test_can_update_last_name()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/auth/update-profile', [
                'last_name' => 'Smith',
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'user_profile_updated_successfully',
            ]);

        $this->user->refresh();
        $this->assertEquals('Smith', $this->user->last_name);
    }

    public function test_can_update_multiple_fields()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/auth/update-profile', [
                'first_name' => 'Alice',
                'last_name' => 'Johnson',
                'phone_number' => '+33123456789',
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'user_profile_updated_successfully',
            ]);

        $this->user->refresh();
        $this->assertEquals('Alice', $this->user->first_name);
        $this->assertEquals('Johnson', $this->user->last_name);
        $this->assertEquals('+33123456789', $this->user->phone_number);
    }

    public function test_can_update_email()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/auth/update-profile', [
                'email' => '<EMAIL>',
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'user_profile_updated_successfully',
            ]);

        $this->user->refresh();
        $this->assertEquals('<EMAIL>', $this->user->email);
    }

    public function test_cannot_update_to_existing_email()
    {
        User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/auth/update-profile', [
                'email' => '<EMAIL>',
            ]);

        $response->assertStatus(422);
    }

    public function test_cannot_update_to_existing_phone_number()
    {
        User::factory()->create([
            'phone_number' => '+33123456789',
        ]);

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/auth/update-profile', [
                'phone_number' => '+33123456789',
            ]);

        $response->assertStatus(422);
    }

    public function test_returns_updated_user_data()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/auth/update-profile', [
                'first_name' => 'Bob',
                'last_name' => 'Wilson',
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'user' => [
                        'id',
                        'email',
                        'first_name',
                        'last_name',
                        'phone_number',
                        'date_of_birth',
                        'status',
                        'created_at',
                        'updated_at',
                    ],
                ],
            ])
            ->assertJson([
                'data' => [
                    'user' => [
                        'first_name' => 'Bob',
                        'last_name' => 'Wilson',
                    ],
                ],
            ]);
    }
}
