<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\RefreshToken;
use App\Models\User;
use App\Services\OtpService;
use Illuminate\Support\Facades\Hash;
use Tests\FeatureTestCase;

class RefreshTokenTest extends FeatureTestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        // Update the user created by parent with additional properties
        $this->user->update([
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'is_active' => true,
        ]);
    }

    public function test_login_returns_refresh_token()
    {
        // Mock the OTP service to always verify successfully
        $this->mock(OtpService::class, function ($mock) {
            $mock->shouldReceive('verifyOtp')
                ->once()
                ->with('+1234567890', '123456')
                ->andReturn(true);
        });

        $response = $this->postJson('/api/v1/auth/login-with-otp', [
            'phone' => '+1234567890',
            'otp' => '123456',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'user',
                    'access_token',
                    'refresh_token',
                    'token_type',
                ],
            ]);

        $data = $response->json('data');
        $this->assertNotEmpty($data['refresh_token']);
        $this->assertEquals('Bearer', $data['token_type']);
    }

    public function test_register_returns_refresh_token()
    {
        $userData = [
            'user_type' => 'customer',
            'full_name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '+1987654321',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'date_of_birth' => '1990-01-01',
            'address' => '123 Test St',
            'ward' => 'Test Ward',
            'district' => 'Test District',
            'city' => 'Test City',
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'user',
                    'access_token',
                    'refresh_token',
                    'token_type',
                ],
            ]);

        $data = $response->json('data');
        $this->assertNotEmpty($data['refresh_token']);
        $this->assertEquals('Bearer', $data['token_type']);
    }

    public function test_refresh_token_success()
    {
        // Create a refresh token for the user
        $tokens = $this->createRefreshTokenForUser($this->user);

        $response = $this->withHeader('Authorization', 'Bearer ' . $tokens['access_token'])
            ->postJson('/api/v1/auth/refresh-token', [
                'refresh_token' => $tokens['refresh_token'],
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'user',
                    'access_token',
                    'refresh_token',
                    'token_type',
                ],
            ]);

        $data = $response->json('data');
        $this->assertNotEmpty($data['access_token']);
        $this->assertNotEmpty($data['refresh_token']);
        $this->assertEquals('Bearer', $data['token_type']);

        // Verify the old refresh token is no longer valid
        $this->assertDatabaseMissing('refresh_tokens', [
            'token' => Hash::make($tokens['refresh_token']),
        ]);
    }

    public function test_refresh_token_validation_required()
    {
        $response = $this->postJson('/api/v1/auth/refresh-token', []);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'data' => [
                    'refresh_token' => ['The refresh token is required.'],
                ],
            ]);
    }

    public function test_refresh_token_validation_min_length()
    {
        $response = $this->postJson('/api/v1/auth/refresh-token', [
            'refresh_token' => 'short',
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'data' => [
                    'refresh_token' => ['The refresh token is invalid.'],
                ],
            ]);
    }

    public function test_refresh_token_invalid_token()
    {
        $response = $this->postJson('/api/v1/auth/refresh-token', [
            'refresh_token' => 'invalid_token_that_does_not_exist_in_database_should_be_long_enough',
        ]);

        $response->assertStatus(401)
            ->assertJson([
                'success' => false,
                'message' => 'Token refresh failed',
            ]);
    }

    public function test_refresh_token_expired_token()
    {
        // Create an expired refresh token
        $accessTokenObject = $this->user->createToken('expired-test-token');
        $accessTokenId = $accessTokenObject->accessToken->id;

        $plainTextToken = 'expired_token_that_should_be_long_enough_for_validation';
        $expiredToken = RefreshToken::create([
            'user_id' => $this->user->id,
            'access_token_id' => $accessTokenId,
            'token' => Hash::make($plainTextToken),
            'expires_at' => now()->subDay(),
        ]);

        $response = $this->postJson('/api/v1/auth/refresh-token', [
            'refresh_token' => $plainTextToken,
        ]);

        $response->assertStatus(401)
            ->assertJson([
                'success' => false,
                'message' => 'Token refresh failed',
            ]);
    }

    public function test_refresh_token_inactive_user()
    {
        // Deactivate the user
        $this->user->update(['is_active' => false]);

        $tokens = $this->createRefreshTokenForUser($this->user);

        $response = $this->withHeader('Authorization', 'Bearer ' . $tokens['access_token'])
            ->postJson('/api/v1/auth/refresh-token', [
                'refresh_token' => $tokens['refresh_token'],
            ]);

        $response->assertStatus(401)
            ->assertJson([
                'success' => false,
                'message' => 'Token refresh failed',
            ]);
    }

    public function test_logout_removes_only_current_device_refresh_token()
    {
        // Create multiple refresh tokens for the user (simulating multiple devices)
        $tokens1 = $this->createRefreshTokenForUser($this->user);
        $tokens2 = $this->createRefreshTokenForUser($this->user);

        // Use device 1's access token for logout
        $accessToken = $tokens1['access_token'];

        // Verify both tokens exist
        $this->assertEquals(2, RefreshToken::where('user_id', $this->user->id)->count());

        // Get device 1's access token ID for later verification
        $device1AccessTokenId = \Laravel\Sanctum\PersonalAccessToken::findToken(
            explode('|', $tokens1['access_token'])[1]
        )->id;

        // Logout from device 1
        $response = $this->postJson('/api/v1/auth/logout', [], [
            'Authorization' => "Bearer $accessToken",
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'User logged out successfully',
            ]);

        // Verify only device 1's refresh token is removed, device 2 remains
        $this->assertEquals(1, RefreshToken::where('user_id', $this->user->id)->count());

        // Verify the remaining refresh token is not associated with device 1
        $remainingToken = RefreshToken::where('user_id', $this->user->id)->first();
        $this->assertNotNull($remainingToken);
        $this->assertNotEquals($device1AccessTokenId, $remainingToken->access_token_id);

        // Verify device 1's access token is deleted
        $this->assertDatabaseMissing('personal_access_tokens', ['id' => $device1AccessTokenId]);
    }

    public function test_refresh_token_rotation()
    {
        // Create initial refresh token
        $originalTokens = $this->createRefreshTokenForUser($this->user);
        $originalTokenCount = RefreshToken::where('user_id', $this->user->id)->count();

        // Use refresh token
        $response = $this->withHeader('Authorization', 'Bearer ' . $originalTokens['access_token'])
            ->postJson('/api/v1/auth/refresh-token', [
                'refresh_token' => $originalTokens['refresh_token'],
            ]);

        $response->assertStatus(200);

        $data = $response->json('data');
        $newRefreshToken = $data['refresh_token'];

        // Verify we still have the same number of tokens (old one deleted, new one created)
        $this->assertEquals($originalTokenCount, RefreshToken::where('user_id', $this->user->id)->count());

        // Verify the new token is different from the original
        $this->assertNotEquals($originalTokens['refresh_token'], $newRefreshToken);

        // Verify the original token can't be used again
        $response = $this->withHeader('Authorization', 'Bearer ' . $originalTokens['access_token'])
            ->postJson('/api/v1/auth/refresh-token', [
                'refresh_token' => $originalTokens['refresh_token'],
            ]);

        $response->assertStatus(401);
    }

    /**
     * Helper method to create a refresh token for a user
     */
    private function createRefreshTokenForUser(User $user): array
    {
        // Create access token first
        $accessTokenObject = $user->createToken('test-token');
        $accessToken = $accessTokenObject->plainTextToken;
        $accessTokenId = $accessTokenObject->accessToken->id;

        $plainTextToken = str()->random(80);

        RefreshToken::create([
            'user_id' => $user->id,
            'access_token_id' => $accessTokenId,
            'token' => Hash::make($plainTextToken),
            'expires_at' => now()->addDays(30),
        ]);

        return [
            'access_token' => $accessToken,
            'refresh_token' => $plainTextToken,
        ];
    }
}
