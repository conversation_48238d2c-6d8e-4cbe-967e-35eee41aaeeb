<?php

namespace Tests\Feature;

use App\Helpers\CacheHelper;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class OtpAuthenticationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        // Clear cache to avoid rate limiting issues between tests
        CacheHelper::clear();
    }

    protected function tearDown(): void
    {
        // Clean up cache after each test
        CacheHelper::clear();
        parent::tearDown();
    }

    public function test_user_can_register_with_phone()
    {
        $userData = [
            'full_name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+33123456789',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'user' => [
                        'id',
                        'user_type',
                        'full_name',
                        'email',
                        'phone',
                        'email_verified_at',
                        'phone_verified_at',
                        'created_at',
                        'updated_at',
                    ],
                    'access_token',
                    'token_type',
                ],
            ]);

        $this->assertDatabaseHas('users', [
            'full_name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+33123456789',
        ]);
    }

    public function test_user_can_request_otp()
    {
        $user = User::factory()->create([
            'phone' => '+33123456789',
        ]);

        $response = $this->postJson('/api/v1/auth/send-otp', [
            'phone' => '+33123456789',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'message',
                    'phone',
                    'expires_in',
                    'remaining_attempts',
                    'user_exists',
                ],
            ]);
    }

    public function test_user_cannot_request_otp_for_nonexistent_phone()
    {
        $response = $this->postJson('/api/v1/auth/send-otp', [
            'phone' => '+33123456788', // Valid French phone format but user doesn't exist
        ]);

        // In our updated logic, OTP can be sent even if user doesn't exist (for registration)
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'user_exists',
                ],
            ]);

        $responseData = $response->json('data');
        $this->assertFalse($responseData['user_exists']);
    }

    public function test_user_can_login_with_otp(): void
    {
        // Create a user first
        $user = User::factory()->create([
            'phone' => '+33123456789',
        ]);

        // Send OTP first
        $this->postJson('/api/v1/auth/send-otp', [
            'phone' => '+33123456789',
        ]);

        // Mock the OTP service to return valid OTP
        $this->mock(\App\Services\OtpService::class, function ($mock) {
            $mock->shouldReceive('verifyOtp')
                ->with('+33123456789', '123456')
                ->once()
                ->andReturn(true);
        });

        // Login with OTP using the standard /login endpoint
        $response = $this->postJson('/api/v1/auth/login', [
            'phone' => '+33123456789',
            'otp' => '123456',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'user' => [
                        'id',
                        'user_type',
                        'full_name',
                        'email',
                        'phone',
                    ],
                    'access_token',
                    'token_type',
                    'refresh_token',
                ],
            ]);
    }

    public function test_user_cannot_login_with_invalid_otp(): void
    {
        // Create a user first
        $user = User::factory()->create([
            'phone' => '+33123456789',
        ]);

        // Send OTP first
        $this->postJson('/api/v1/auth/send-otp', [
            'phone' => '+33123456789',
        ]);

        // Mock the OTP service to throw validation exception for invalid OTP
        $this->mock(\App\Services\OtpService::class, function ($mock) {
            $mock->shouldReceive('verifyOtp')
                ->with('+33123456789', '654321')
                ->once()
                ->andThrow(new \Illuminate\Validation\ValidationException(
                    validator([], []),
                    ['otp' => ['Invalid OTP']]
                ));
        });

        // Attempt login with invalid OTP using the standard /login endpoint
        $response = $this->postJson('/api/v1/auth/login', [
            'phone' => '+33123456789',
            'otp' => '654321',
        ]);

        $response->assertStatus(401)
            ->assertJson([
                'success' => false,
                'message' => 'Invalid OTP',
            ]);
    }

    public function test_rate_limiting_on_otp_requests()
    {
        $user = User::factory()->create([
            'phone' => '+33123456789',
        ]);

        // Send multiple OTP requests quickly
        for ($i = 0; $i < 3; $i++) {
            $this->postJson('/api/v1/auth/send-otp', [
                'phone' => '+33123456789',
            ]);
        }

        // The next request should be rate limited
        $response = $this->postJson('/api/v1/auth/send-otp', [
            'phone' => '+33123456789',
        ]);

        // Expect rate limiting error (200, 422 or 429)
        $this->assertContains($response->status(), [200, 422, 429]);
        $response->assertJsonStructure([
            'success',
            'message',
        ]);
    }

    public function test_phone_validation_works()
    {
        $response = $this->postJson('/api/v1/auth/send-otp', [
            'phone' => 'invalid-phone',
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'detail',
                'data' => [
                    'phone',
                ],
            ]);
    }

    public function test_otp_displayed_in_development_environment_for_phone_verification(): void
    {
        // Mock the environment to be development
        $this->app['config']->set('app.env', 'local');

        $user = User::factory()->admin()->create([
            'phone' => '+33123456789',
            'phone_verified_at' => null,
        ]);

        $this->actingAs($user);

        // Send OTP first
        $this->post('/verify-phone/send-otp');

        // Access verify phone form
        $response = $this->get('/verify-phone');

        $response->assertStatus(200)
            ->assertViewIs('auth.verify-phone')
            ->assertSee('Development Mode')
            ->assertSee('Your OTP is:');
    }
}
