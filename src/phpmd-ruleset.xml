<?xml version="1.0"?>

<ruleset name="Rules"
         xmlns="http://pmd.sf.net/ruleset/1.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://pmd.sf.net/ruleset/1.0.0 http://pmd.sf.net/ruleset_xml_schema.xsd"
         xsi:noNamespaceSchemaLocation="http://pmd.sf.net/ruleset_xml_schema.xsd">
    <description>
Primarily based on the unused code ruleset but altered to remove UnusedFormalParameter as this check does not take into account parent method declarations.
    </description>

    <!-- Import entire naming rule set and exclude rules -->
    <rule ref="vendor/phpmd/phpmd/src/main/resources/rulesets/unusedcode.xml">
        <exclude name="UnusedFormalParameter" />
        <exclude name="UnusedLocalVariable" />
    </rule>

    <!-- Import portions of cleancode -->
    <rule ref="vendor/phpmd/phpmd/src/main/resources/rulesets/cleancode.xml">
        <exclude name="BooleanArgumentFlag" />
        <exclude name="ElseExpression" />
        <exclude name="IfStatementAssignment" />
        <exclude name="StaticAccess" />
        <exclude name="MissingImport" />
        <exclude name="UndefinedVariable" />
    </rule>
</ruleset>
