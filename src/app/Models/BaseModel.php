<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\Builder;

class BaseModel extends Model
{
    use HasFactory;

    protected $dateFormat = DateTimeInterface::ATOM;

    protected $defaultColumns = [];

    /**
     * Prepare a date for array / JSON serialization.
     *
     * We enforce ISO8601 with timezone offset format.
     *
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format(DateTimeInterface::ATOM);
    }

    /**
     * Custom messages for validation.
     *
     * These will be determined by the child class.
     */
    public static function messages(): array
    {
        return [];
    }

    /**
     * Check if a table has been joined or not
     */
    public static function isJoined($query, $table)
    {
        $joins = null;

        if ($query instanceof EloquentBuilder) {
            $joins = $query->getQuery()->joins;
        } elseif ($query instanceof Builder) {
            $joins = $query->joins;
        }

        if ($joins == null) {
            return false;
        }

        // This now supports tables that have been aliased
        foreach ($joins as $join) {
            if (strpos($join->table, $table) !== false) {
                return true;
            }
        }

        return false;
    }

    public function getDefaultColumns(): array
    {
        // Allow the default columns to be configured manually per model
        if (is_array($this->defaultColumns) && !empty($this->defaultColumns)) {
            return $this->defaultColumns;
        }

        // Alternative method
        // $columns = \Schema::getColumnListing(with(new static)->getTable());

        return array_keys($this->casts);
    }
}
