<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;

class File extends BaseModel
{
    use HasUuids;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'files';

    /**
     * The primary key type is string (UUID).
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'fileable_id',
        'fileable_type',
        'type',
        'path',
        'size',
        'etag',
        'sha256_hash',
        'mime_type',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'id' => 'string',
        'fileable_id' => 'string',
        'fileable_type' => 'string',
        'type' => 'string',
        'path' => 'string',
        'size' => 'integer',
        'etag' => 'string',
        'sha256_hash' => 'string',
        'mime_type' => 'string',
    ];

    /**
     * Get the parent fileable model (user, product, etc.).
     */
    public function fileable()
    {
        return $this->morphTo();
    }
}
