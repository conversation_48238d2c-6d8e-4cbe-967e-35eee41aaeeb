<?php

namespace App\Models\Traits;

use App\Scopes\ActiveScope;
use Kafka\Models\IRS\Application;

/**
 * Only return active records by default unless we are using an application
 * that specifically wants inactive records as well (Eg: the admin backend).
 */
trait EnforceActive
{
    /**
     * <PERSON><PERSON> runs this automatically on instantiation of the trait/model.
     *
     * @return void
     */
    protected static function bootEnforceActive()
    {
        static::addGlobalScope(new ActiveScope);
    }
}
