<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Hash;

class RefreshToken extends BaseModel
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'access_token_id',
        'token',
        'expires_at',
        'device_info',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'expires_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'token',
    ];

    /**
     * Default token expiry time in days
     */
    const DEFAULT_EXPIRY_DAYS = 30;

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->expires_at)) {
                $model->expires_at = now()->addDays(self::DEFAULT_EXPIRY_DAYS);
            }
        });
    }

    /**
     * Check if the token is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at->isPast();
    }

    /**
     * Check if the token is valid (not expired)
     */
    public function isValid(): bool
    {
        return !$this->isExpired();
    }

    /**
     * Revoke the refresh token
     */
    public function revoke(): bool
    {
        return $this->delete();
    }

    /**
     * Find a valid refresh token by token string
     */
    public static function findValidToken(string $plainTextToken): ?self
    {
        // Get all valid tokens and check hash
        $refreshTokens = self::valid()->get();

        foreach ($refreshTokens as $refreshToken) {
            if (Hash::check($plainTextToken, $refreshToken->token)) {
                return $refreshToken;
            }
        }

        return null;
    }

    /**
     * Find a valid refresh token by access token ID
     */
    public static function findByAccessTokenId(int $accessTokenId): ?self
    {
        return self::valid()
            ->where('access_token_id', $accessTokenId)
            ->first();
    }

    /**
     * Revoke all refresh tokens for a user
     */
    public static function revokeAllForUser(User $user): int
    {
        return self::where('user_id', $user->id)->delete();
    }

    /**
     * Clean up expired tokens
     */
    public static function cleanupExpired(): int
    {
        return self::where('expires_at', '<', now())->delete();
    }

    // Relationships

    /**
     * Get the user that owns the refresh token
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the access token that this refresh token belongs to
     */
    public function accessToken(): BelongsTo
    {
        return $this->belongsTo(\Laravel\Sanctum\PersonalAccessToken::class, 'access_token_id');
    }

    // Scopes

    /**
     * Scope for valid (non-expired) tokens
     */
    public function scopeValid($query)
    {
        return $query->where('expires_at', '>', now());
    }

    /**
     * Scope for expired tokens
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<=', now());
    }
}
