<?php

namespace App\Http;

use Illuminate\Http\JsonResponse;

/**
 * Class: ApiResponse
 *
 * This extends the default laravel JsonResponse with our own custom data
 * structure.
 *
 * All responses from our API are returned in this standard format.
 *
 * @see JsonResponse
 */
class ApiResponse extends JsonResponse
{
    /**
     * Constructor.
     *
     * @param  string  $message  Human readable message
     * @param  bool  $apiSuccess  Was the API request successful
     * @param  array  $data  Response metadata (pagination etc)
     * @param  int  $status  HTTP status code
     * @param  array  $headers  HTTP headers
     * @param  int  $options  Options
     */
    public function __construct(
        $message,
        $apiSuccess = true,
        $data = null,
        $metadata = [],
        $status = 200,
        $headers = [],
        $detail = null,
        $options = 0,
        $errors = null
    ) {
        $data = [
            'success' => $apiSuccess,
            'message' => $message,
            'detail' => $detail,
            'data' => $data,
        ];

        if (!empty($errors)) {
            $data['errors'] = $errors;
        }

        if ($metadata !== []) {
            $data['metadata'] = $metadata;
        }

        parent::__construct($data, $status, $headers, $options);
    }
}
