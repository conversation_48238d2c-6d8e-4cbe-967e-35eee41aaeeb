<?php

namespace App\Http\Controllers;

use App\Helpers\ApiMessages;
use App\Http\ApiResponse;
use App\Http\Resources\API\ApiResource;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Support\Str;

abstract class Controller
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    /**
     * Send a generic CRUD response
     */
    public function sendCrudResponse(string $type, mixed $resource, string $primaryKey = 'id'): ApiResponse
    {
        $types = [
            'store' => 'created',
            'show' => 'retrieved',
            'update' => 'updated',
            'destroy' => 'deleted',
        ];

        if ($resource instanceof ApiResource) {
            $resourceName = Str::humanClassName($resource->resource);
        } else {
            $resourceName = Str::humanClassName($resource);
        }

        $message = sprintf(
            '%s %s %s successfully',
            $resourceName,
            $resource->$primaryKey,
            $types[$type]
        );

        return $this->sendResponse($message, $resource);
    }

    public function sendResponse(
        ApiMessages|string $message,
        $data = [],
        $metadata = [],
        $code = 200,
        $headers = []
    ): ApiResponse {
        $detail = null;

        if ($message instanceof ApiMessages) {
            $detail = $message->getDetailedMessage();
            $message = $message->getLocalisedMessage();
        }

        return new ApiResponse($message, true, $data, $metadata, $code, $headers, $detail);
    }

    public function sendError(
        $error,
        $data = [],
        $metadata = [],
        $code = 404,
        $headers = []
    ): ApiResponse {
        return new ApiResponse($error, false, $data, $metadata, $code, $headers);
    }
}
