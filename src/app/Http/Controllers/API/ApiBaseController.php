<?php

namespace App\Http\Controllers\API;

use App\Http\ApiResponse;
use App\Http\Controllers\Controller;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class ApiBaseController extends Controller
{

    /**
     * Send paginated response.
     */
    public function sendPaginatedResponse(string $message, LengthAwarePaginator $paginator, array $extra = []): ApiResponse
    {
        return $this->sendResponse(
            $message,
            $paginator->items(),
            array_merge([
                'pagination' => [
                    'current_page' => $paginator->currentPage(),
                    'last_page' => $paginator->lastPage(),
                    'total' => $paginator->total(),
                    'per_page' => $paginator->perPage(),
                ],
            ], $extra)
        );
    }
}
