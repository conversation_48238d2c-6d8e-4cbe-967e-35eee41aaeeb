<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\API\ApiBaseController;
use App\Http\Requests\API\V1\Auth\ForgotPasswordRequest;
use App\Http\Requests\API\V1\Auth\LoginRequest;
use App\Http\Requests\API\V1\Auth\RefreshTokenRequest;
use App\Http\Requests\API\V1\Auth\ResetPasswordRequest;
use App\Http\Requests\API\V1\Auth\UpdateProfileRequest;
use App\Http\Requests\API\V1\Auth\UpdatePasswordRequest;
use App\Http\Requests\API\V1\Auth\VerifyResetOtpRequest;
use App\Services\AuthService;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class AuthController extends ApiBaseController
{
    protected AuthService $authService;

    public function __construct(AuthService $authService)
    {
        $this->authService = $authService;
    }

    /**
     * Login user with email and password
     */
    public function login(LoginRequest $request)
    {
        $result = $this->authService->login($request->only('email', 'password'));

        return $this->sendResponse('user_logged_in_successfully', $result);
    }

    /**
     * Logout user (Revoke the token)
     */
    public function logout(Request $request)
    {
        $this->authService->logout($request->user());

        return $this->sendResponse('user_logged_out_successfully');
    }

    /**
     * Refresh access token using refresh token
     */
    public function refreshToken(RefreshTokenRequest $request)
    {
        $result = $this->authService->refreshToken($request->refresh_token);

        return $this->sendResponse('token_refreshed_successfully', $result);
    }

    /**
     * Get the authenticated user's profile
     */
    public function profile(Request $request)
    {
        $user = $request->user();

        if (!$user) {
            return $this->sendError('unauthenticated', [], [], Response::HTTP_UNAUTHORIZED);
        }

        $result = $this->authService->getUserProfile($user);

        return $this->sendResponse('user_profile_retrieved_successfully', $result);
    }

    /**
     * Update the authenticated user's profile
     */
    public function updateProfile(UpdateProfileRequest $request)
    {
        try {
            $result = $this->authService->updateProfile(
                $request->user(),
                $request->only([
                    'first_name',
                    'last_name',
                    'email',
                    'phone_number',
                    'date_of_birth'
                ])
            );

            return $this->sendResponse('user_profile_updated_successfully', $result);
        } catch (ValidationException $e) {
            return $this->sendError(
                'profile_update_failed',
                $e->errors(),
                [],
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }
    }

    /**
     * Initiate forgot password process - send OTP to registered email
     */
    public function forgotPassword(ForgotPasswordRequest $request)
    {
        $result = $this->authService->initiateForgotPassword($request->email);

        return $this->sendResponse('otp_sent_successfully', $result);
    }

    /**
     * Verify OTP for password reset
     */
    public function verifyResetOtp(VerifyResetOtpRequest $request)
    {
        $result = $this->authService->verifyPasswordResetOtp($request->email, $request->otp);

        return $this->sendResponse('otp_verified_successfully', $result);
    }

    /**
     * Reset password using reset token
     */
    public function resetPassword(ResetPasswordRequest $request)
    {
        $result = $this->authService->resetPassword($request->email, $request->password, $request->token);
        return $this->sendResponse('password_reset_success', $result);
    }

    /**
     * Update password for authenticated user
     */
    public function updatePassword(UpdatePasswordRequest $request)
    {
        $result = $this->authService->updatePassword($request->user(), $request->current_password, $request->password);
        return $this->sendResponse('password_updated_successfully', $result);
    }
}
