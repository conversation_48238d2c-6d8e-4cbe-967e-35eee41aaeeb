<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\API\ApiBaseController;
use App\Http\Requests\API\V1\Camps\DuplicateCampRequest;
use App\Http\Requests\API\V1\Camps\StoreCampRequest;
use App\Http\Requests\API\V1\Camps\UpdateCampRequest;
use App\Repositories\CampRepository;
use App\Services\CampService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\CampActionLogger;

class CampController extends ApiBaseController
{
    const int POST_PER_PAGE = 20;

    public function __construct(
        protected CampService    $campService,
        protected CampRepository $campRepository,
        protected CampActionLogger $campActionLogger
    ) {
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $filters = $request->only(['name', 'start_date', 'end_date', 'location', 'min_price', 'max_price']);
        $perPage = $request->get('per_page', $this::POST_PER_PAGE);

        $camps = $this->campService->filterCamps($filters, $perPage);

        return $this->sendPaginatedResponse('camp_listed_successfully', $camps);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreCampRequest $request): JsonResponse
    {
        $camp = $this->campService->create($request->validated());

        return $this->sendResponse('camp_created_successfully', $camp, '', Response::HTTP_CREATED);
    }

    /**
     * Display the specified resource.
     */
    public function show(int $id): JsonResponse
    {
        $camp = $this->campRepository->find($id);
        if (!$camp) {
            return $this->sendError('camp_not_found', [], Response::HTTP_NOT_FOUND);
        }

        return $this->sendResponse('retrieve_camp_detail_successfully', $camp);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateCampRequest $request, int $id): JsonResponse
    {
        $camp = $this->campRepository->find($id);
        if (!$camp) {
            return $this->sendError('camp_not_found', [], Response::HTTP_NOT_FOUND);
        }
        $updatedCamp = $this->campService->update($camp, $request->validated());

        return $this->sendResponse('camp_updated_successfully', $updatedCamp);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(int $id): JsonResponse
    {
        $camp = $this->campRepository->find($id);
        if (!$camp) {
            return $this->sendError('camp_not_found', Response::HTTP_NOT_FOUND);
        }

        $this->campService->delete($camp);

        return $this->sendResponse('camp_deleted_successfully', ['id' => $id]);
    }

    public function duplicate(int $id, DuplicateCampRequest $duplicateCampRequest): JsonResponse
    {
        $camp = $this->campRepository->find($id);
        if (!$camp) {
            return $this->sendError('camp_not_found', [], Response::HTTP_NOT_FOUND);
        }

        $data = $duplicateCampRequest->validated();
        $newCamp = $this->campService->duplicate($data, $camp);
        $this->campActionLogger->log('controller action duplicated', $newCamp);

        return $this->sendResponse('camp_duplicated_successfully', $newCamp, '', Response::HTTP_CREATED);
    }
}
