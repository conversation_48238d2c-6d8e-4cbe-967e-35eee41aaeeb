<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class RequestLogging
{
    const ENDPOINTS_TO_MONITOR = [
        'api/*',
    ];

    const ENDPOINTS_TO_EXCLUDE = [
        'api/documentation*',
    ];

    protected $start;

    private function endpointIsMonitored($request): bool
    {
        if ($request->is(self::ENDPOINTS_TO_EXCLUDE)) {
            return false;
        }

        return $request->is(self::ENDPOINTS_TO_MONITOR);
    }

    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request): (Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!$this->endpointIsMonitored($request)) {
            return $next($request);
        }

        $this->start = microtime(true);
        $requestId = $request->headers->get('x-request-id');

        // Set up log context with request ID for better traceability
        Log::withContext([
            'request_id' => $requestId,
            'method' => $request->getMethod(),
            'path' => $request->path(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        $msg = sprintf(
            'Request started: %s /%s',
            $request->getMethod(),
            $request->path()
        );

        if ($request->isMethod('GET')) {
            $msg .= ' - params: '.json_encode($request->input());
        }

        Log::info($msg);

        return $next($request);
    }

    public function terminate($request, $response): void
    {
        if (!$this->endpointIsMonitored($request)) {
            return;
        }

        $logLevel = 'notice';
        $state = 'failed';
        $message = 'No response message';
        $isSuccessfulApiCall = false;

        if ($response instanceof JsonResponse) {
            $data = (array) $response->getData();
            $message = array_key_exists('message', $data) ? $data['message'] : $message;

            if ($response->getStatusCode() === Response::HTTP_UNPROCESSABLE_ENTITY) {
                $message .= json_encode($data['data'] ?? []);
            }
            $isSuccessfulApiCall = array_key_exists('success', $data) && $data['success'];
        }

        $isSuccessfulCall = $request->route() !== null && $request->route()->getPrefix() !== 'api' && $response->getStatusCode() === 200;

        if ($isSuccessfulApiCall || $isSuccessfulCall) {
            $state = 'succeeded';
            $logLevel = 'info';
        }

        $duration = microtime(true) - $this->start;
        $statusText = method_exists($response, 'statusText') ?
            $response->statusText() :
            $this->getStatusText($response->getStatusCode());

        // Update log context with response information
        Log::withContext([
            'request_id' => $request->headers->get('x-request-id'),
            'response_id' => $request->headers->get('x-response-id'),
            'status_code' => $response->getStatusCode(),
            'duration' => round($duration, 3),
            'state' => $state,
        ]);

        Log::{$logLevel}(
            sprintf(
                "Request %s: %s (%s %s, Duration: %ss)",
                $state,
                $message,
                $response->getStatusCode(),
                $statusText,
                $duration
            )
        );
    }

    protected function getStatusText(int $statusCode)
    {
        return Response::$statusTexts[$statusCode] ?? 'unknown status';
    }
}
