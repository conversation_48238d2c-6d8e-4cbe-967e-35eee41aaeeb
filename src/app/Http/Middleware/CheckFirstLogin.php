<?php

namespace App\Http\Middleware;

use App\Services\AuthService;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckFirstLogin
{
    public function __construct(protected AuthService $authService)
    {
    }

    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request): (Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $this->authService->getCurrentUser();

        if (!$user->hasCompletedFirstLogin()) {
            if ($request->routeIs('change-password') || $request->routeIs('change-password.submit')) {
                return $next($request);
            }

            return redirect()->route('change-password')
                ->with('info', __('messages.portal.first_login.change_password_required'));
        }

        if ($request->routeIs('change-password') || $request->routeIs('change-password.submit')) {
            if ($user->isRetailer()) {
                return redirect()->route('retailer.dashboard')
                    ->with('info', __('messages.portal.first_login.already_completed'));
            }

            return redirect()->route('admin.dashboard')
                ->with('info', __('messages.portal.first_login.already_completed'));
        }

        if (!$user->phone_verified_at) {
            if ($request->routeIs('verify-phone') ||
                $request->routeIs('verify-phone.submit') ||
                $request->routeIs('verify-phone.send-otp')) {
                return $next($request);
            }

            return redirect()->route('verify-phone')
                ->with('info', __('messages.portal.first_login.verify_phone_required'));
        }

        return $next($request);
    }
}
