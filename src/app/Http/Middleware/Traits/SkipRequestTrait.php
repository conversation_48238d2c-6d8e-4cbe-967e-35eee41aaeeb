<?php

namespace App\Http\Middleware\Traits;

trait SkipRequestTrait
{
    private array $except = [
        'public/*',
    ];

    protected function inExceptArray($request): bool
    {
        foreach ($this->except as $except) {
            if ($except !== '/') {
                $except = trim($except, '/');
            }

            if ($request->fullUrlIs($except) || $request->is($except)) {
                return true;
            }
        }

        return false;
    }
}
