<?php

namespace App\Http\Middleware;

use App\Http\Middleware\Traits\SkipRequestTrait;
use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;

class AddResponseId
{
    use SkipRequestTrait;

    /**
     * Add a unique response ID to all requests & responses.
     *
     * This assists in tracking all API request/response combinations and all
     * related logging.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        /**
         * The uniqid is a representation of the request start time in
         * microseconds which will be unique in the vast majority of cases, the
         * extra random characters should guarantee this.
         *
         * Readability is important as this may be presented to end-users who
         * may need to communicate it back to support so we enforce lower-case.
         *
         * The uniqid can be reverse-engineered back to the original time value
         * which can be useful if we have a response ID but don't know what date
         * it was created on:
         *
         * $responseId = '5e0bef4d5464e.Rb1YYY';
         * echo date("r", hexdec(substr($responseId, 0, 8)));
         *
         * We could use a uuid however we don't need to be globally unique,
         * just (mostly) unique within this application - this is also
         * considerably faster to compute.
         */
        $responseId = uniqid() . '.R' . Str::random(5);

        $request->headers->set('x-response-id', $responseId, false);

        $response = $next($request);

        if ($this->inExceptArray($request)) {
            return $response;
        }

        $response->headers->set('x-response-id', $responseId, false);

        /**
         * We add it to the request data if supported however we ensure it is
         * always available via headers as well.
         */
        if ($response instanceof JsonResponse) {
            $data = $response->getData();
            if (is_object($data)) {
                $data->responseId = $responseId;
            }

            if (is_array($data)) {
                $data['responseId'] = $responseId;
            }
            $response->setData($data);
        }

        return $response;
    }
}
