<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class Authenticate
{
    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request): (Response)  $next
     */
    public function handle(Request $request, Closure $next, ?string $redirectTo = null): Response
    {
        if (!Auth::check()) {
            if ($request->is('admin*') || $request->is('retailer*')) {
                return redirect()->route('login')->with('error', __('messages.portal.auth.login_required'));
            }

            $redirectPath = $redirectTo ?: '/login';

            return redirect($redirectPath)->with('error', __('messages.portal.auth.login_required'));
        }

        $user = Auth::user();

        if (!$user->isActive()) {
            Auth::logout();

            return redirect()->route('login')->with('error', __('messages.portal.auth.account_deactivated'));
        }

        return $next($request);
    }
}
