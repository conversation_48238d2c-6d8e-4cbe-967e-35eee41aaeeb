<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Symfony\Component\HttpFoundation\Response;

class Localization
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get locale from various sources in order of priority
        $locale = $this->getLocale($request);

        // Set the application locale
        App::setLocale($locale);

        return $next($request);
    }

    /**
     * Get locale from request in order of priority
     */
    private function getLocale(Request $request): string
    {
        // 1. Check for explicit locale parameter in request
        if ($request->has('locale')) {
            $requestLocale = $request->get('locale');
            if ($this->isValidLocale($requestLocale)) {
                return $requestLocale;
            }
        }

        // 2. Check Accept-Language header
        $acceptLanguage = $request->header('Accept-Language');
        if ($acceptLanguage) {
            $preferredLocale = $this->parseAcceptLanguage($acceptLanguage);
            if ($this->isValidLocale($preferredLocale)) {
                return $preferredLocale;
            }
        }

        // 3. Check user preference if authenticated
        $user = $request->user();
        if ($user && isset($user->preferred_language) && $this->isValidLocale($user->preferred_language)) {
            return $user->preferred_language;
        }

        // 4. Fall back to default locale
        return config('app.locale', 'en');
    }

    /**
     * Parse Accept-Language header to get preferred language
     */
    private function parseAcceptLanguage(string $acceptLanguage): string
    {
        // Parse Accept-Language header (e.g., "en-US,en;q=0.9,vi;q=0.8")
        $languages = explode(',', $acceptLanguage);

        foreach ($languages as $language) {
            $parts = explode(';', trim($language));
            $locale = trim($parts[0]);

            // Extract just the language part (e.g., "en" from "en-US")
            $languageCode = explode('-', $locale)[0];

            if ($this->isValidLocale($languageCode)) {
                return $languageCode;
            }
        }

        return config('app.locale', 'en');
    }

    /**
     * Check if locale is supported
     */
    private function isValidLocale(string $locale): bool
    {
        $supportedLocales = ['en', 'vi']; // Add more supported locales here

        return in_array($locale, $supportedLocales, true);
    }
}
