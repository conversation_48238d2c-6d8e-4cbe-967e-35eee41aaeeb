<?php

namespace App\Http\Middleware;

use App\Exceptions\BadRequestIdException;
use App\Http\Middleware\Traits\SkipRequestTrait;
use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class AddRequestId
{
    use SkipRequestTrait;

    /**
     * Request ID validation pattern
     */
    private const REQUEST_ID_PATTERN = '/^[a-z0-9]{13}\.Q[a-zA-Z0-9]{5}$/';

    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request): (Response)  $next
     *
     * @throws BadRequestIdException
     */
    public function handle(Request $request, Closure $next): Response
    {
        $requestIdRequired = $this->isRequestIdRequired($request);
        $requestId = $request->headers->get('x-request-id');

        // Auto-generate request ID if missing and required
        if ($requestIdRequired && $requestId === null && !$request->isMethod('options')) {
            $requestId = $this->generateRequestId();
            $request->headers->set('x-request-id', $requestId, false);
        }

        // Validate request ID format if provided
        if ($requestId !== null &&
            !preg_match(self::REQUEST_ID_PATTERN, $requestId) &&
            !$request->isMethod('options')
        ) {
            throw new BadRequestIdException('request_id_invalid');
        }

        $response = $next($request);

        if ($this->inExceptArray($request)) {
            return $response;
        }

        // Always set the request ID in response headers
        $response->headers->set('x-request-id', $requestId, false);

        // Add to JSON response body if applicable
        $this->addRequestIdToJsonResponse($response, $requestId);

        return $response;
    }

    /**
     * Determine if request ID is required for this request
     */
    private function isRequestIdRequired(Request $request): bool
    {
        return $request->is('api/*') && !$request->is('api/documentation*');
    }

    /**
     * Generate a new request ID
     *
     * The uniqid is a representation of the request start time in
     * microseconds which will be unique in the vast majority of cases, the
     * extra random characters should guarantee this.
     *
     * Readability is important as this may be presented to end-users who
     * may need to communicate it back to support so we enforce lower-case.
     *
     * The uniqid can be reverse-engineered back to the original time value
     * which can be useful if we have a request ID but don't know what date
     * it was created on:
     *
     * $requestId = '5e0bef4d39447.Qa0XXX';
     * echo date("r", hexdec(substr($requestId, 0, 8)));
     *
     * We could use a uuid however we don't need to be globally unique,
     * just (mostly) unique within this application - this is also
     * considerably faster to compute.
     */
    private function generateRequestId(): string
    {
        return uniqid() . '.Q' . Str::random(5);
    }

    /**
     * Add request ID to JSON response body
     */
    private function addRequestIdToJsonResponse(Response $response, ?string $requestId): void
    {
        if (!$response instanceof JsonResponse) {
            return;
        }

        $data = $response->getData();

        if (is_object($data)) {
            $data->requestId = $requestId;
        } elseif (is_array($data)) {
            $data['requestId'] = $requestId;
        }

        $response->setData($data);
    }
}
