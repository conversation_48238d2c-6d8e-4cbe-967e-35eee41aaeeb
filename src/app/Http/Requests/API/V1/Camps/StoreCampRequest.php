<?php

namespace App\Http\Requests\API\V1\Camps;

use App\Enums\CampStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreCampRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:500',
            ],
            'description' => 'required|string|max:500',
            'internal_note' => 'nullable|string|max:1000',
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after_or_equal:start_date',
            'location' => 'required|string|max:255',
            'max_capacity' => 'required|integer|min:1',
            'price' => 'required|integer|min:0',
            'status' => Rule::in(CampStatus::values()),

            // Prevent editing created_by and updated_by
            'created_by' => 'prohibited',
            'updated_by' => 'prohibited',
        ];
    }
}
