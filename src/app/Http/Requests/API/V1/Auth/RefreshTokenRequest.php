<?php

declare(strict_types=1);

namespace App\Http\Requests\API\V1\Auth;

use App\Http\Requests\API\APIRequest;

class RefreshTokenRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'refresh_token' => ['required', 'string', 'min:50'],
        ];
    }
}
