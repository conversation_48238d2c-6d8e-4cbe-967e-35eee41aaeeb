<?php

declare(strict_types=1);

namespace App\Http\Requests\API\V1\Auth;

use App\Http\Requests\API\APIRequest;

class VerifyResetOtpRequest extends APIRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'email' => ['required', 'string', 'email'],
            'otp' => ['required', 'string', 'size:6'],
        ];
    }
}
