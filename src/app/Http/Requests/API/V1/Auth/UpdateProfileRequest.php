<?php

declare(strict_types=1);

namespace App\Http\Requests\API\V1\Auth;

use App\Rules\PhoneNumber;
use Illuminate\Foundation\Http\FormRequest;

class UpdateProfileRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $userId = $this->user()?->id;

        return [
            'email' => 'sometimes|required|email|max:255|unique:users,email,' . $userId,
            'date_of_birth' => 'sometimes|nullable|date_format:Y-m-d',
            'first_name' => 'sometimes|string|max:255',
            'last_name' => 'sometimes|string|max:255',
            'phone_number' => ['sometimes', 'nullable', 'string', new PhoneNumber, 'unique:users,phone_number,' . $userId]
        ];
    }
}
