<?php

declare(strict_types=1);

namespace App\Http\Requests\API\V1\Auth;

use App\Http\Requests\API\APIRequest;
use App\Rules\PhoneNumber;

class VerifyRegisterOtpRequest extends APIRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'phone' => ['required', 'string', new PhoneNumber],
            'otp' => ['required', 'string', 'size:6'],
        ];
    }
}
