<?php

declare(strict_types=1);

namespace App\Http\Requests\API\V1\Auth;

use App\Http\Requests\API\APIRequest;
use App\Rules\PasswordValidation;

class ResetPasswordRequest extends APIRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'email' => ['required', 'string', 'email'],
            'token' => ['required', 'string'],
            'password' => ['required', 'string', new PasswordValidation, 'confirmed'],
            'password_confirmation' => ['required', 'string'],
        ];
    }
}
