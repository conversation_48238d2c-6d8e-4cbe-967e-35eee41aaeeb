<?php

namespace App\Http\Requests\API;

use App\Rules\RangeFilterFormat\RangeFilterFormat as RangeFilterFormatRule;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rules\Unique;

class APIRequest extends FormRequest
{
    /**
     * Used to suppress validation for certain attributes that contain an array
     */
    protected array $arrayAttributesToIgnoreForUnexpectedAttributes = [];

    /**
     * We can optionally override this method in child classes to perform more
     * custom validation.
     *
     * This method gets called automatically when the base FormRequest gets its
     * validator instance.
     *
     * By default we mark any unexpected attributes as errors.
     */
    public function withValidator(Validator $validator)
    {
        $validator->after(function (Validator $validator) {
            $rules = $validator->getRules();

            $this->validateUnexpectedAttributes($validator, $validator->attributes(), $rules);
        });
    }

    protected function validateUnexpectedAttributes(
        Validator $validator,
        array $attributes,
        array $rules,
        string $parentRule = '',
    ) {
        $isAssoc = Arr::isAssoc($attributes);
        foreach ($attributes as $attribute => $value) {
            $validatingAttribute = $parentRule . $attribute;

            // We skip over this check when we have been passed a non-associated array
            // as the attribute will be a numeric array index.
            if ($isAssoc && !array_key_exists($validatingAttribute, $rules)) {
                $validator->errors()->add($validatingAttribute, sprintf('Unexpected attribute \'%s\'', $attribute));

                continue;
            }

            // Beware this is recursive
            if (is_array($value) && !in_array($attribute, $this->arrayAttributesToIgnoreForUnexpectedAttributes)) {
                $this->validateUnexpectedAttributes($validator, $value, $rules, $parentRule . $attribute . '.');

                continue;
            }

            if (!is_array($value) && !$parentRule && !$isAssoc) {
                $validator->errors()->add($validatingAttribute, sprintf('Unexpected attribute \'%s\'', $attribute));
            }
        }
    }

    /**
     * Simple helper shortcut to merge rule-sets together.
     *
     * There is almost certainly a better way of doing this at a global level.
     */
    protected function getMergedRules(mixed $newRule, mixed $rules, bool $prepend = false): mixed
    {
        if (!is_array($rules)) {
            $rules = explode('|', $rules);
        }

        if ($prepend) {
            array_unshift($rules, $newRule);
        } else {
            $rules[] = $newRule;
        }

        return $rules;
    }

    /**
     * We have various models that require uniqueness enforced on some
     * attributes (usually in combination with the tenant organisation check).
     *
     * This is used by update requests so that the current model under
     * validation is not included in the uniqueness check.
     */
    protected function ignoreCurrentModelForUniquenessCheck(array &$rules, string $attribute, string $id): void
    {
        if (!isset($rules[$attribute])) {
            return;
        }

        foreach ($rules[$attribute] as $key => $rule) {
            if ($rule instanceof Unique) {
                $rules[$attribute][$key] = $rule->ignore($id);
                break;
            }
        }
    }

    /**
     * Get any custom validation messages from the model.
     */
    public function messages(): array
    {
        if (method_exists($this, 'model') && method_exists($this->model(), 'messages')) {
            return $this->model()::messages();
        }

        return [];
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        if (method_exists($this, 'model') && method_exists($this->model(), 'rules')) {
            $rules = $this->model()::rules();

            if ($this->isMethod('patch')) {
                return $this->filterRulesForPatch($rules);
            }

            return $rules;
        }

        return [];
    }

    public function mergeNewParamByParam(
        string $model,
        $sourceColumn,
        $sourceValue,
        $destinationParam,
        $destinationColumn
    ) {
        $sourceField = (new $model)->where($sourceColumn, $sourceValue)->first();
        if (!empty($sourceField)) {
            $this->merge([
                $destinationParam => $sourceField->{$destinationColumn},
            ]);
        }
    }

    public function getPriceFieldsFiltering()
    {
        return $this->priceFieldsFiltering ?? [];
    }

    public function getPriceFilterColumns(): array
    {
        $priceFields = [];
        foreach ($this->all() as $parameter => $value) {
            $priceFieldFilter = [];
            if (in_array($parameter, $this->getPriceFieldsFiltering())) {
                $values = explode('|', $value);
                $priceFieldFilter['parameter'] = $parameter;
                $operator = $values[0];

                if (count($values) === 1 || $operator === RangeFilterFormatRule::OPERATOR_EQUAL) {
                    $price = count($values) === 1 ? $values[0] : $values[1];

                    $priceFieldFilter['operator'] = RangeFilterFormatRule::OPERATOR_EQUAL;
                    $priceFieldFilter['value'] = $price;
                    $priceFields[] = $priceFieldFilter;

                    continue;
                }

                $priceFieldFilter['operator'] = $operator;

                if ($operator === RangeFilterFormatRule::OPERATOR_BETWEEN) {
                    $priceFieldFilter['value'] = [$values[1], $values[2]];
                    $priceFields[] = $priceFieldFilter;

                    continue;
                }

                $priceFieldFilter['value'] = $values[1];
                $priceFields[] = $priceFieldFilter;
            }
        }

        return $priceFields;
    }

    /**
     * Requests that use the PATCH method only contain some attributes so
     * we only need apply validatation for the present attributes.
     *
     * Additionally we include validation rules that use required_with as
     * these are used to define attibute dependancies.
     *
     * Eg:
     * Request body: [
     *      'is_default' => true,
     *      'name' => 'Test'
     *      'price' => 500
     * ]
     *
     * Default rules: [
     *      'tax_rate_id' => 'required_with:price|integer',
     *      'price' => 'required:price|integer',
     *      'is_default' => 'required|boolean',
     *      'name' => 'required|string|max:255',
     *      'is_active' => 'required|boolean',
     * ]
     *
     * After filter, rules only has: [
     *      'tax_rate_id' => 'required_with:price|integer',
     *      'price' => 'required:price|integer',
     *      'is_default' => 'required|boolean',
     *      'name' => 'required|string|max:255',
     * ]
     */
    public function filterRulesForPatch(array $rules, array $keys = []): array
    {
        $keys = !empty($keys) ? $keys : array_keys($this->all());
        $availableRuleKeys = array_keys($rules);
        $filteredRules = [];

        // We always include rules that use required_with
        foreach ($availableRuleKeys as $key) {
            // Handle string based rules
            if (!is_array($rules[$key])) {
                $rules[$key] = explode('|', $rules[$key]);
            }
            foreach ($rules[$key] as $rule) {
                if (is_string($rule) && preg_match('/^required_with:/', $rule)) {
                    $filteredRules[$key] = $rules[$key];
                    break;
                }
            }
        }

        foreach ($keys as $key) {
            if (!empty($rules[$key])) {
                $filteredRules[$key] = $rules[$key];
            }

            $childRuleKeys = preg_grep('/^[' . $key . ']+\.(.*)$/', $availableRuleKeys);
            foreach ($childRuleKeys as $childRuleKey) {
                if (!empty($rules[$childRuleKey])) {
                    $filteredRules[$childRuleKey] = $rules[$childRuleKey];
                }
            }
        }

        return $filteredRules;
    }
}
