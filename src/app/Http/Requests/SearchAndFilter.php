<?php

namespace App\Http\Requests;

use App\Rules\RangeFilterFormat\DateFilterFormat as DateFilterFormatRule;
use App\Rules\RangeFilterFormat\RangeFilterFormat as RangeFilterFormatRule;
use Carbon\Carbon;

trait SearchAndFilter
{
    /**
     * Note that sort_column is deliberately not included here so that we can
     * ensure a check against the column name.
     *
     * The child class is responsible for implementing validation on that.
     *
     * @var array
     */
    protected $genericQueryParameterRules = [
        'sort_order' => 'nullable|required_with:sort_column|in:asc,desc',
    ];

    /**
     * Search requests contain generic pagination query criteria, child classes
     * may optionally support additional searchable columns or criteria and
     * will need to include these generic rules as well.
     *
     * All query parameters are optional - by default most requests will have
     * no search parameters at all.
     */
    public function genericQueryParameterRules(): array
    {
        return array_merge(
            $this->getPaginationRules(),
            $this->queryDateParameterRules(),
            $this->genericQueryParameterRules,
        );
    }

    public function getDateFieldsFiltering()
    {
        return $this->dateFieldsFiltering ?? ['created_at', 'updated_at'];
    }

    protected function queryDateParameterRules()
    {
        $rules = [];

        foreach ($this->getDateFieldsFiltering() as $value) {
            $rules[$value] = ['sometimes', new DateFilterFormatRule];
        }

        return $rules;
    }

    /**
     * We override the parent method so that we can convert and sanitize search
     * values before validation.
     *
     * @SuppressWarnings(PHPMD.UnusedLocalVariable)
     */
    protected function prepareForValidation()
    {
        parent::prepareForValidation();

        /**
         * Sanitize boolean values
         *
         * This is quite inefficient as we iterate through every single
         * parameter on each search request.
         *
         * If we ever need to perform more sanitization than this then building
         * a more generic system will be required.
         *
         * We are probably better off setting an array of parameters that need
         * to be sanitized and explicitly operating only on those parameters.
         *
         * The following may be helpful in that scenario:
         *
         * https://github.com/Waavi/Sanitizer.git
         */
        foreach (parent::all() as $parameter => $value) {
            if ($this->isBooleanRequired($parameter)) {
                $this->sanitizeBooleanValue($parameter, $value);
            }
        }

        /**
         * Auto append limit and page information incase they are not supplied
         */
        if (!$this->limit) {
            $defaultLimit = $this->getDefaultRecordLimit();
            if ($defaultLimit) {
                $this->merge([
                    'limit' => $defaultLimit,
                ]);

                $this->appendFirstPage();
            }
        } else {
            $this->appendFirstPage();
        }
    }

    private function appendFirstPage()
    {
        if (!$this->page) {
            $this->merge(['page' => 1]);
        }
    }

    /**
     * Determine if the supplied parameter requires a boolean value.
     *
     * @param  mixed  $parameter
     */
    protected function isBooleanRequired($parameter): bool
    {
        if (array_key_exists($parameter, $this->rules())) {
            $rules = $this->rules()[$parameter];

            if (is_string($rules)) {
                $rules = explode('|', $rules);
            }

            return in_array('boolean', $rules);
        }

        return false;
    }

    /**
     * Convert the supplied value to a boolean.
     *
     * If we don't understand the input then it will be passed through
     * un-altered and will fail validation.
     *
     * @param  string  $parameter
     * @param  string  $value
     */
    protected function sanitizeBooleanValue($parameter, $value)
    {
        $booleanValue = filter_var($this->query($parameter), FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);

        if ($booleanValue !== null) {
            $this->merge([$parameter => $booleanValue]);
        }
    }

    /**
     * This strips out any generic or "comparison" query parameters and should
     * just leave us with the actual columns being queried.
     *
     * @SuppressWarnings(PHPMD.UnusedLocalVariable)
     */
    public function getFilterColumns()
    {
        $genericQueryParameters = array_keys($this->genericQueryParameterRules());

        if (array_key_exists('sort_column', $this->rules())) {
            $genericQueryParameters[] = 'sort_column';
        }

        foreach ($this->rules() as $parameter => $rule) {
            if ($this->isComparisonParameter($parameter)) {
                $genericQueryParameters[] = $parameter;
            }
        }

        return $this->except($genericQueryParameters);
    }

    /**
     * This returns any 'extra' criteria related to search filtering that isn't
     * the actual value we are searching for.
     *
     * For instance, we might include a comparator of '>' to check if a dollar
     * amount is above a certain value.
     *
     * @var array
     */
    public function getFilterColumnComparisonCriteria(): array
    {
        $criteria = [];
        $criteria['normal'] = [];
        $criteria['range'] = [];

        foreach ($this->all() as $parameter => $value) {
            if ($this->isComparisonParameter($parameter)) {
                $filterParameter = str_replace('_criteria', '', $parameter);
                $criteria['normal'][$filterParameter] = $value;
            }
        }

        $criteria['range']['date'] = $this->getDateFilterColumns();
        $criteria['range']['price'] = $this->getPriceFilterColumns();

        return $criteria;
    }

    public function getDateFilterColumns(): array
    {
        $dateFields = [];
        foreach ($this->all() as $parameter => $value) {
            $dateFieldFilter = [];
            if ($this->isDateFiltering($parameter)) {
                if (empty($value) || $value === RangeFilterFormatRule::OPERATOR_IS_NULL) {
                    $dateFields[] = [
                        'parameter' => $parameter,
                        'operator' => RangeFilterFormatRule::OPERATOR_IS_NULL,
                    ];

                    continue;
                }

                $values = explode('|', $value);
                $dateFieldFilter['parameter'] = $parameter;
                $operator = $values[0];

                if (count($values) === 1 || $operator === RangeFilterFormatRule::OPERATOR_EQUAL) {
                    $date = count($values) === 1 ? $values[0] : $values[1];

                    if (DateFilterFormatRule::isSpecificDateTimeFormat($date)) {
                        $dateFieldFilter['operator'] = RangeFilterFormatRule::OPERATOR_EQUAL;
                        $dateFieldFilter['value'] = $this->getDateValue($date);
                        $dateFields[] = $dateFieldFilter;

                        continue;
                    }

                    $dateFieldFilter['operator'] = RangeFilterFormatRule::OPERATOR_BETWEEN;
                    $dateFieldFilter['value'] = [$this->getDateValue($date), $this->getDateValue($date, 'endOfDay')];
                    $dateFields[] = $dateFieldFilter;

                    continue;
                }

                $dateFieldFilter['operator'] = $operator;

                if ($operator === RangeFilterFormatRule::OPERATOR_BETWEEN) {
                    $startDate = $this->getDateValue($values[1], 'startOfDay');
                    $endDate = $this->getDateValue($values[2], 'endOfDay');

                    $dateFieldFilter['value'] = [$startDate, $endDate];
                    $dateFields[] = $dateFieldFilter;

                    continue;
                }

                if ($operator === RangeFilterFormatRule::OPERATOR_GREATER_THAN || $operator === RangeFilterFormatRule::OPERATOR_LESS_THAN_OR_EQUAL) {
                    $dateFieldFilter['value'] = $this->getDateValue($values[1], 'endOfDay');
                } else {
                    $dateFieldFilter['value'] = $this->getDateValue($values[1], 'startOfDay');
                }

                $dateFields[] = $dateFieldFilter;
            }
        }

        return $dateFields;
    }

    private function getDateValue(string $value, string $method = 'startOfDay'): string
    {
        $timezone = config('app.timezone');

        if (DateFilterFormatRule::isSpecificDateTimeFormat($value)) {
            return Carbon::parse($value, $timezone)->format('Y-m-d H:i:sO');
        }

        return Carbon::parse($value, $timezone)->$method()->format('Y-m-d H:i:sO');
    }

    protected function isDateFiltering($parameterName)
    {
        return in_array($parameterName, $this->getDateFieldsFiltering());
    }

    /**
     * Determine if the supplied name is a comparison variable.
     *
     * @param  string  $parameterName
     */
    protected function isComparisonParameter($parameterName): bool
    {
        return strpos($parameterName, '_criteria') !== false;
    }

    /**
     * Define pagination rules
     */
    protected function getPaginationRules(): array
    {
        $commonOptions = '|integer|min:1';
        $limitOption = $this->paginationRequired() ? 'required' : 'sometimes';
        $pageOption = $this->paginationRequired() ? 'required_with:limit' : 'sometimes';
        $rules = [
            'page' => $pageOption . $commonOptions,
            'limit' => $limitOption . $commonOptions
                . '|max:' . $this->getMaxRecordLimit(),
        ];

        return $rules;
    }

    /**
     * This is a default record limit
     */
    protected function getDefaultRecordLimit(): ?int
    {
        if (property_exists($this, 'defaultRecordLimit')) {
            return $this->defaultRecordLimit;
        }

        return null;
    }

    /**
     * This is max records limit
     */
    protected function getMaxRecordLimit(): int
    {
        if (property_exists($this, 'maxRecordLimit')) {
            return $this->maxRecordLimit;
        }

        return 100; // Default max record limit
    }

    /**
     * Force pagination (page and limit) or not
     */
    protected function paginationRequired(): bool
    {
        if (property_exists($this, 'paginationRequired')) {
            return $this->paginationRequired;
        }

        return false;
    }
}
