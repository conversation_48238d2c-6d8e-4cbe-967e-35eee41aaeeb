<?php

namespace App\Providers;

use <PERSON>doc\Scramble\Scramble;
use Dedoc\Scramble\Support\Generator\OpenApi;
use Dedoc\Scramble\Support\Generator\SecurityScheme;
use Illuminate\Support\ServiceProvider;
use App\Channels\EmailChannel;
use Illuminate\Support\Str;
use Illuminate\Routing\Route as RoutingRoute;
use libphonenumber\PhoneNumberUtil;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->registerEmailChannel();
        $this->registerPhoneNumberUtil();
        $this->registerScramble();
    }

    /**
     * Register EmailChannel as singleton
     */
    private function registerEmailChannel(): void
    {
        $this->app->singleton(EmailChannel::class, function ($app) {
            return new EmailChannel();
        });
    }

    /**
     * Register PhoneNumberUtil as singleton
     */
    private function registerPhoneNumberUtil(): void
    {
        $this->app->singleton('PhoneNumberUtil', function ($app) {
            return PhoneNumberUtil::getInstance();
        });
    }

    private function registerScramble(): void
    {
        Scramble::configure()
            ->withDocumentTransformers(function (OpenApi $openApi) {
                $openApi->secure(
                    SecurityScheme::http('bearer')
                );
            })
            ->routes(function (RoutingRoute $route) {
                return Str::startsWith($route->uri, 'api/');
            });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
    }
}
