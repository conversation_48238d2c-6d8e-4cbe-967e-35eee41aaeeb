<?php

namespace App\Providers;

use App\Interfaces\CampRepositoryInterface;
use App\Repositories\CampRepository;
use Illuminate\Support\ServiceProvider;

class RepositoryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->bind(
            CampRepositoryInterface::class,
            CampRepository::class
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
