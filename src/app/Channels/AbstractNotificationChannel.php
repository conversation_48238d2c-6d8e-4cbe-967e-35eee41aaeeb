<?php

declare(strict_types=1);

namespace App\Channels;

use Illuminate\Support\Facades\Log;

abstract class AbstractNotificationChannel
{
    /**
     * Send notification with rate limiting
     */
    public function send(string $destination, string $message, array $options = []): bool
    {
        Log::info('Sending notification', [
            'destination' => $destination,
            'channel' => static::class,
        ]);

        try {
            $result = $this->sendNotification($destination, $message, $options);

            if ($result) {
                Log::info('Notification sent successfully', [
                    'destination' => $destination,
                    'channel' => static::class,
                ]);
            } else {
                Log::warning('Notification send failed', [
                    'destination' => $destination,
                    'channel' => static::class,
                ]);
            }

            return $result;
        } catch (\Throwable $e) {
            Log::error('Notification send exception', [
                'destination' => $destination,
                'channel' => static::class,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Abstract method to be implemented by concrete channels
     */
    abstract protected function sendNotification(string $destination, string $message, array $options = []): bool;
}
