<?php

declare(strict_types=1);

namespace App\Channels;

use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use App\Mail\OtpMail;

class EmailChannel extends AbstractNotificationChannel
{
    /**
     * Send email notification
     */
    protected function sendNotification(string $destination, string $message, array $options = []): bool
    {
        try {
            $mailData = [
                'otp' => $message,
                'type' => $options['type'] ?? 'otp',
                'subject' => $options['subject'] ?? 'Your OTP Code',
                'template' => $options['template'] ?? 'emails.otp',
                'identifier' => $destination,
            ];

            Mail::to($destination)->send(new OtpMail($mailData));

            Log::info('Email sent successfully', [
                'destination' => $destination,
                'type' => $mailData['type'],
            ]);

            return true;
        } catch (\Throwable $e) {
            Log::error('Email send failed', [
                'destination' => $destination,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Send password reset OTP email
     */
    public function sendPasswordResetOtp(string $email, string $otp): bool
    {
        return $this->send($email, $otp, [
            'type' => 'password_reset',
            'subject' => 'Password Reset OTP',
            'template' => 'emails.password-reset-otp',
        ]);
    }

    /**
     * Send registration OTP email
     */
    public function sendRegistrationOtp(string $email, string $otp): bool
    {
        return $this->send($email, $otp, [
            'type' => 'registration',
            'subject' => 'Registration OTP',
            'template' => 'emails.registration-otp',
        ]);
    }
}
