<?php

namespace App\Common\Traits;

use App\Helpers\CacheHelper;
use Closure;
use Exception;
use Illuminate\Contracts\Cache\LockTimeoutException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

trait Caching
{
    protected $maxTimeForCachingLock = 10;

    protected $maxTimeForCachingWait = 5;

    protected $cachePrefix;

    protected $isUseTag = true;

    protected $acceptNullData = false;

    public $nullString = 'null';

    public function getCacheKey($key): string
    {
        $prefix = $this->cachePrefix !== null ? trim($this->cachePrefix, '|') : null;

        if ($this instanceof Model) {
            $prefix = $prefix ?: $this->getTable();
        }

        if (!$prefix) {
            return $key;
        }

        return "{$prefix}|{$key}";
    }

    protected function getProvider()
    {
        return CacheHelper::store();
    }

    public function connection()
    {
        return $this->getProvider()->getStore();
    }

    public function getFromCacheForever($key, Closure $callback)
    {
        return $this->retryGetData($key, $callback);
    }

    protected function getData($key, Closure $callback, ?int $seconds = null)
    {
        // Prepend the table name to the cache key
        $key = $this->getCacheKey($key);

        if (!$this->getProvider()->has($key)) {
            $this->lock($key, fn () => $this->getProvider()->put($key, $callback(), $seconds));
        }

        $result = $this->getProvider()->get($key);
        if ($result === $this->nullString) {
            return null;
        }

        return $result;
    }

    /**
     * @throws Exception
     */
    protected function retryGetData($key, Closure $callback, ?int $seconds = null)
    {
        $maxRetries = 3;
        $attempt = 0;

        while ($attempt < $maxRetries) {
            try {
                $data = $this->getData($key, $callback, $seconds);

                // Handle null string conversion
                if ($data === $this->nullString) {
                    return null;
                }

                // Check if data is empty and we don't accept null data
                if (empty($data) && !$this->acceptNullData) {
                    $this->clearCache($key);
                    Log::warning("Empty data returned for cache key: {$key}");
                    throw new Exception("Failed to get data from cache for key: {$key}");
                }

                return $data;
            } catch (Exception $e) {
                $attempt++;
                Log::warning("Failed to get data key '{$key}' from cache, attempt {$attempt} out of {$maxRetries}", [
                    'error' => $e->getMessage(),
                    'attempt' => $attempt,
                ]);

                if ($attempt >= $maxRetries) {
                    Log::error("Failed to get data key '{$key}' from cache after {$maxRetries} retries");
                    throw $e;
                }

                // Wait before retrying (exponential backoff)
                usleep(100000 * $attempt); // 100ms, 200ms, 300ms
            }
        }
    }

    public function getDataFromCache($key, Closure $callback, ?int $seconds = 86400)
    {
        return $this->retryGetData($key, $callback, $seconds);
    }

    public function flushAll()
    {
        return CacheHelper::clear();
    }

    public function getFromCache($key, $defaultValue = null)
    {
        return $this->getProvider()->get($this->getCacheKey($key), $defaultValue);
    }

    public function putToCache($key, $value, $seconds)
    {
        // Prepend the table name to the cache key
        $key = $this->getCacheKey($key);
        $this->lock($key, fn () => $this->getProvider()->put($key, $value, $seconds));
    }

    public function clearCache($key)
    {
        // Prepend the table name to the cache key
        $key = $this->getCacheKey($key);
        $this->lock($key, fn () => $this->getProvider()->forget($key));
    }

    protected function lock(string $key, Closure $callback)
    {
        $lock = CacheHelper::store()->lock("{$key}-lock", $this->maxTimeForCachingLock);
        try {
            $lock->block($this->maxTimeForCachingWait);
            $callback();
        } catch (LockTimeoutException $e) {
            Log::error($e->getMessage());
        } finally {
            $lock?->release();
        }
    }
}
