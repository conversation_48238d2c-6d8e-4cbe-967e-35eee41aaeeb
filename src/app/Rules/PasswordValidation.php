<?php

declare(strict_types=1);

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class PasswordValidation implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (strlen($value) < 8 ||
            strlen($value) > 64 ||
            !preg_match('/[A-Z]/', $value) ||
            !preg_match('/[a-z]/', $value) ||
            !preg_match('/[0-9]/', $value) ||
            !preg_match('/[@#$!%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]/', $value) ||
            preg_match('/\s/', $value)) {
            $fail(__('validation.password_format'));
        }
    }
}
