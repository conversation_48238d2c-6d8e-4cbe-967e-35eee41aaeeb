<?php

namespace App\Rules;

use App\Helpers\Utils;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use libphonenumber\NumberParseException;
use libphonenumber\PhoneNumberFormat;
use libphonenumber\PhoneNumberType;

class PhoneNumber implements ValidationRule
{
    const FRANCE_REGION_CODE = 'FR';
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if ($value instanceof PhoneNumberType) {
            $value = Utils::formatPhoneNumber($value);
        }

        if (empty($value)) {
            return;
        }

        if (!preg_match('/^[\+]?[0-9\s\-\(\)]+$/', $value)) {
            $fail(__('validation.phone_number_invalid_format'));
            return;
        }

        if (app()->bound('PhoneNumberUtil')) {
            try {
                $parsedValue = app('PhoneNumberUtil')->parse($value);

                if (!app('PhoneNumberUtil')->isValidNumber($parsedValue)) {
                    $fail(__('validation.phone_number_invalid'));
                    return;
                }

                $regionCode = app('PhoneNumberUtil')->getRegionCodeForNumber($parsedValue);
                if ($regionCode !== self::FRANCE_REGION_CODE) {
                    $fail(__('validation.phone_number_france'));
                    return;
                }
            } catch (NumberParseException $e) {
                if (!preg_match('/^[\+]?[0-9]{8,15}$/', preg_replace('/[\s\-\(\)]/', '', $value))) {
                    $fail(__('validation.phone_number_invalid'));
                    return;
                }
            }
        } else {
            $cleanNumber = preg_replace('/[\s\-\(\)]/', '', $value);
            if (!preg_match('/^[\+]?[0-9]{8,15}$/', $cleanNumber)) {
                $fail(__('validation.phone_number_invalid'));
                return;
            }
        }
    }
}
