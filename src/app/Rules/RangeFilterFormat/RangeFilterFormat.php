<?php

namespace App\Rules\RangeFilterFormat;

use Illuminate\Contracts\Validation\Rule;

/**
 * Range filter format
 */
class RangeFilterFormat implements Rule
{
    const OPERATOR_BETWEEN = 'between';

    const OPERATOR_EQUAL = '=';

    const OPERATOR_GREATER_THAN = '>';

    const OPERATOR_GREATER_THAN_OR_EQUAL = '>=';

    const OPERATOR_LESS_THAN_OR_EQUAL = '<=';

    const OPERATOR_LESS_THAN = '<';

    const OPERATOR_IS_NULL = 'null';

    const OPERATORS = [
        self::OPERATOR_GREATER_THAN,
        self::OPERATOR_GREATER_THAN_OR_EQUAL,
        self::OPERATOR_LESS_THAN,
        self::OPERATOR_LESS_THAN_OR_EQUAL,
        self::OPERATOR_EQUAL,
        self::OPERATOR_BETWEEN,
    ];

    protected $errorMsg;

    protected $values;

    protected $operator;

    /**
     * @param  string  $attribute
     * @param  mixed  $value
     */
    public function passes($attribute, $value): bool
    {
        $this->values = explode('|', $value);

        return true;
    }

    /**
     * @return bool
     */
    protected function validationValues()
    {
        if (empty($this->values)) {
            $this->errorMsg = 'No values found';

            return false;
        }

        return true;
    }

    /**
     * @return bool
     */
    protected function validateOperators()
    {
        $this->operator = $this->values[0];
        if (!in_array($this->operator, self::OPERATORS)) {
            $this->errorMsg = sprintf("Invalid operator '%s'", $this->operator);

            return false;
        }

        return true;
    }

    /**
     * @return bool
     */
    protected function validateValuesIsValidOperatorBetween()
    {
        if ($this->operator !== self::OPERATOR_BETWEEN && count($this->values) > 2) {
            $this->errorMsg = "Multiple values are only supported when the operator is 'between'";

            return false;
        }

        return true;
    }

    /**
     * @return array|string
     */
    public function message()
    {
        $msg = 'The :attribute is invalid';

        if ($this->errorMsg !== null) {
            $msg .= ': ' . $this->errorMsg;
        }

        return $msg;
    }
}
