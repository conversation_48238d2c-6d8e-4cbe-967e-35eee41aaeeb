<?php

namespace App\Rules\RangeFilterFormat;

/**
 * Date filter format
 */
class DateFilterFormat extends RangeFilterFormat
{
    public function passes($attribute, $value): bool
    {
        parent::passes($attribute, $value);

        if (!$this->validationValues()) {
            return false;
        }

        if (count($this->values) === 1) {
            if ($this->validateDateValue($this->values[0])) {
                return true;
            }

            $this->errorMsg = 'No valid :attribute found';

            return false;
        }

        if (!$this->validateOperators()) {
            return false;
        }

        if (!$this->validateDateValue($this->values[1])) {
            $this->errorMsg = 'No valid :attribute found';

            return false;
        }

        if ($this->operator === self::OPERATOR_BETWEEN &&
            (count($this->values) > 3 || !isset($this->values[2]) || !$this->validateDateValue($this->values[2]))
        ) {
            $this->errorMsg = "No valid secondary :attribute found";

            return false;
        }

        if (!$this->validateValuesIsValidOperatorBetween()) {
            return false;
        }

        return true;
    }

    private function validateDateValue(string $dateValue): bool
    {
        return self::isSpecificDateTimeFormat($dateValue) || self::validateDateValueWithFormat($dateValue, 'Y-m-d');
    }

    public static function isSpecificDateTimeFormat(string $dateValue)
    {
        return self::validateDateValueWithFormat($dateValue, 'Y-m-d\TH:i:s');
    }

    public static function validateDateValueWithFormat(string $dateValue, string $format)
    {
        $parsed = date_parse_from_format($format, $dateValue);

        return $parsed['error_count'] === 0 && $parsed['warning_count'] === 0;
    }
}
