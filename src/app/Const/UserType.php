<?php

declare(strict_types=1);

namespace App\Const;

enum UserType: string
{
    case CUSTOMER = 'customer';
    case ADMIN = 'admin';
    case RETAILER = 'retailer';

    /**
     * Get display name for the user type
     */
    public function getDisplayName(): string
    {
        return match ($this) {
            self::CUSTOMER => 'Customer',
            self::ADMIN => 'Admin',
            self::RETAILER => 'Retailer',
        };
    }

    /**
     * Get all user types as array
     */
    public static function toArray(): array
    {
        return array_map(fn ($case) => $case->value, self::cases());
    }

    /**
     * Get user type options for forms/dropdowns
     */
    public static function getOptions(): array
    {
        $options = [];
        foreach (self::cases() as $case) {
            $options[$case->value] = $case->getDisplayName();
        }

        return $options;
    }

    /**
     * Check if user type can authenticate with password
     */
    public function canAuthenticateWithPassword(): bool
    {
        return match ($this) {
            self::ADMIN, self::RETAILER => true,
            self::CUSTOMER => false, // Customers use OTP primarily
        };
    }

    /**
     * Check if user type can authenticate with OTP
     */
    public function canAuthenticateWithOtp(): bool
    {
        return $this === self::CUSTOMER;
    }
}
