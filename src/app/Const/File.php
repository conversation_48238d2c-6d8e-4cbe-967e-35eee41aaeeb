<?php

declare(strict_types=1);

namespace App\Const;

enum File: string
{
    case FILE = 'file';
    case IMAGE = 'image';
    case PHOTO = 'photo';
    case DOCUMENT = 'document';
    case VIDEO = 'video';
    case AUDIO = 'audio';
    case ARCHIVE = 'archive';
    case AVATAR = 'avatar';
    case LOGO = 'logo';
    case BANNER = 'banner';
    case ICON = 'icon';
    case MANUAL = 'manual';
    case CERTIFICATE = 'certificate';
    case INVOICE = 'invoice';
    case RECEIPT = 'receipt';

    /**
     * Get all file types as array.
     */
    public static function toArray(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get file types as options array for forms.
     */
    public static function getOptions(): array
    {
        return array_combine(
            self::toArray(),
            array_map('ucfirst', self::toArray())
        );
    }

    /**
     * Check if a file type is valid.
     */
    public static function isValid(string $type): bool
    {
        return in_array($type, self::toArray(), true);
    }

    /**
     * Get file type by value.
     */
    public static function fromValue(string $value): ?self
    {
        foreach (self::cases() as $case) {
            if ($case->value === $value) {
                return $case;
            }
        }

        return null;
    }

    /**
     * Get display name for the file type.
     */
    public function getDisplayName(): string
    {
        return ucfirst($this->value);
    }

    /**
     * Check if this file type is an image.
     */
    public function isImage(): bool
    {
        return in_array($this, [self::IMAGE, self::AVATAR, self::LOGO, self::BANNER, self::ICON]);
    }

    /**
     * Check if this file type is a document.
     */
    public function isDocument(): bool
    {
        return in_array($this, [self::DOCUMENT, self::MANUAL, self::CERTIFICATE, self::INVOICE, self::RECEIPT]);
    }

    /**
     * Check if this file type is media.
     */
    public function isMedia(): bool
    {
        return in_array($this, [self::VIDEO, self::AUDIO]);
    }
}
