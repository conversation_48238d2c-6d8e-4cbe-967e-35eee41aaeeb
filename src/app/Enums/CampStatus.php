<?php

namespace App\Enums;

enum CampStatus: string
{
    case Active = 'active';
    case InProgress = 'in_progress';
    case Cancelled = 'cancelled';
    case Full = 'full';
    case Completed = 'completed';
    case Deleted = 'deleted';

    public static function values(): array
    {
        return array_map(fn ($case) => $case->value, self::cases());
    }

    public static function labels(): array
    {
        return [
            self::Active->value => 'Active',
            self::InProgress->value => 'In Progress',
            self::Cancelled->value => 'Cancelled',
            self::Full->value => 'Full',
            self::Completed->value => 'Completed',
            self::Deleted->value => 'Deleted',
        ];
    }
}
