<?php

namespace App\Repositories;

use App\Enums\CampStatus;
use App\Interfaces\CampRepositoryInterface;
use App\Models\Camp;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use App\Const\Common;

class CampRepository implements CampRepositoryInterface
{
    public function all(): Collection
    {
        return Camp::all();
    }

    public function find(int $id): ?Camp
    {
        return Camp::find($id);
    }

    public function create(array $data): Camp
    {
        return Camp::create($data);
    }

    public function update(Camp $camp, array $data): bool
    {
        return $camp->update($data);
    }

    public function delete(Camp $camp): bool
    {
        return $camp->delete();
    }

    public function duplicate(Camp $camp, array $data): Camp
    {
        $newCampData = $camp->toArray();

        unset($newCampData['id'], $newCampData['created_at'], $newCampData['updated_at']);

        $newCampData['status'] = CampStatus::Active;
        $newCampData['start_date'] = $data['start_date'] ?? $camp->start_date;
        $newCampData['end_date'] = $data['end_date'] ?? $camp->end_date;

        return $this->create(data: $newCampData);
    }

    /**
     * Filter camps based on various criteria.
     *
     * @return Collection
     */
    public function filter(array $filters, int $perPage = Common::ITEM_PER_PAGE): LengthAwarePaginator
    {
        return Camp::query()
            ->when(!empty($filters['name']), function ($query) use ($filters) {
                $query->where('name', 'like', $filters['name'] . '%');
            })
            ->when(!empty($filters['location']), function ($query) use ($filters) {
                $query->where('location', 'like', $filters['location'] . '%');
            })
            ->when(!empty($filters['start_date']), function ($query) use ($filters) {
                $query->whereDate('start_date', '>=', $filters['start_date']);
            })
            ->when(!empty($filters['end_date']), function ($query) use ($filters) {
                $query->whereDate('end_date', '<=', $filters['end_date']);
            })
            ->when(!empty($filters['min_price']), function ($query) use ($filters) {
                $query->where('price', '>=', $filters['min_price']);
            })
            ->when(!empty($filters['max_price']), function ($query) use ($filters) {
                $query->where('price', '<=', $filters['max_price']);
            })
            ->orderBy('start_date', 'asc')
            ->paginate($perPage);
    }
}
