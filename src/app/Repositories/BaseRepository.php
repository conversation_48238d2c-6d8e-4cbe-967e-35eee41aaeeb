<?php

namespace App\Repositories;

use App\Common\Traits\Caching;
use App\Helpers\Utils;
use App\Models\BaseModel;
use App\Rules\RangeFilterFormat\RangeFilterFormat;
use Exception;
use Illuminate\Container\Container as Application;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Traits\ForwardsCalls;

abstract class BaseRepository
{
    use Caching;
    use ForwardsCalls;

    /**
     * @var BaseModel|Model
     */
    protected $model;

    protected $modelClass;

    /**
     * @var Application
     */
    protected $app;

    /**
     * Store global scopes to be excluded
     */
    protected $excludedGlobalScopes = [];

    /**
     * Allow setting of excluded scopes
     */
    public function setExcludedGlobalScopes($scopes)
    {
        $this->excludedGlobalScopes = [];
        $existingScopes = $this->model->getGlobalScopes();
        foreach ($scopes as $scope) {
            if (array_key_exists($scope, $existingScopes)) {
                $this->excludedGlobalScopes[] = $scope;
            }
        }
    }

    public function getFillable()
    {
        return (new $this->model)->getFillable();
    }

    /**
     * Reset global scopes
     */
    public function resetGlobalScopes()
    {
        $this->excludedGlobalScopes = [];
    }

    /**
     * Allow retrieval of excluded scopes
     */
    public function getExcludedGlobalScopes()
    {
        return $this->excludedGlobalScopes;
    }

    /**
     * Allow retrieval of model global scopes
     */
    public function getGlobalScopes()
    {
        return $this->model->getGlobalScopes();
    }

    /**
     * @throws Exception
     */
    public function __construct()
    {
        $this->makeModel();
    }

    /**
     * Configure the model
     */
    public function model()
    {
        return $this->modelClass;
    }

    /**
     * Provided access to the model
     */
    public function getModel()
    {
        return $this->model;
    }

    /**
     * Make Model instance
     *
     * @return Model
     *
     * @throws Exception
     */
    public function makeModel()
    {
        $model = app($this->model());

        if (!$model instanceof Model) {
            throw new Exception("Class {$this->modelClass} must be an instance of Illuminate\\Database\\Eloquent\\Model");
        }
        $this->cachePrefix = $model->getTable();

        return $this->model = $model;
    }

    protected function applySortOrder(
        Builder $query,
        $sortColumn = null,
        $sortOrder = null,
    ) {
        if ($sortColumn !== null && $sortOrder !== null) {
            $query->orderBy($this->model->getTable() . '.' . $sortColumn, $sortOrder);
        }

        if ($sortColumn === null && $sortOrder === null && Schema::hasColumn($this->model->getTable(), 'created_at')) {
            $query->orderBy($this->model->getTable() . '.created_at', 'desc');
        }
    }

    /**
     * Build a query for retrieving all records.
     *
     * @param  array  $search
     * @param  int|null  $limit
     * @param  string|null  $sortColumn
     * @param  string|null  $sortOrder
     * @param  array  $comparisonCriteria
     * @param  null|string  $customSearchCriteria
     * @return Builder
     */
    public function allQuery(
        $search = [],
        $columns = null,
        $limit = null,
        $sortColumn = null,
        $sortOrder = null,
        $comparisonCriteria = [],
        $customSearchCriteria = null
    ) {
        $query = $this->model->query();

        $this->applySortOrder($query, $sortColumn, $sortOrder);

        if ($columns && is_array($columns)) {
            $query->select($columns);
        } else {
            $query->select($this->model->getDefaultColumns());
        }

        if ($customSearchCriteria) {
            $this->$customSearchCriteria($search, $query, $comparisonCriteria);
        } else {
            $this->addSearchCriteria($search, $query, $comparisonCriteria);
        }

        if ($limit !== null) {
            $query->limit($limit);
        }

        return $query;
    }

    /**
     * We support searching via column names, defaulting to exact matches and
     * optionally supporting like, less-than or greater-than queries.
     *
     * This may be overridden by child classes when more complex criteria is
     * required.
     *
     * @param  array  $search  (request)
     */
    protected function addSearchCriteria(array $search, Builder $query, array $comparisonCriteria)
    {
        $table = $this->model->getTable();
        $this->addSearchCriteriaToQuery($search, $query, $comparisonCriteria, $table);
    }

    protected function addSearchCriteriaToQuery(array $search, Builder|QueryBuilder $query, array $comparisonCriteria, string $table)
    {
        $normalComparisonCriteria = isset($comparisonCriteria['normal']) ? $comparisonCriteria['normal'] : [];
        $rangeComparisonCriteria = isset($comparisonCriteria['range']) ? $comparisonCriteria['range'] : [];

        foreach ($rangeComparisonCriteria as $rangeComparisonCriterionField) {
            foreach ($rangeComparisonCriterionField as $value) {
                $key = "{$table}.{$value['parameter']}";

                // Remove price criteria from normal search
                Arr::pull($search, $value['parameter']);

                if ($value['operator'] === RangeFilterFormat::OPERATOR_IS_NULL) {
                    $query->whereNull($key);

                    continue;
                }

                if ($value['operator'] === RangeFilterFormat::OPERATOR_BETWEEN) {
                    $this->between($query, $key, $value['value']);

                    continue;
                }

                $query->where($key, $value['operator'], $value['value']);
            }
        }

        foreach ($search as $key => $value) {
            if (!empty($value)) {
                $value = Utils::mapSpecialQuote($value);
            }

            // Add advanced searching criteria to the query
            //            if ($key == 'search') {
            //                $this->advancedSearch($query, $value);
            //                continue;
            //            }

            // Skip sort column and order
            if (in_array($key, ['sort_column', 'sort_order'])) {
                continue;
            }

            $key = "{$table}.{$key}";
            if (array_key_exists($key, $normalComparisonCriteria)) {
                $query->where($key, $normalComparisonCriteria[$key], $value);
            } elseif ($value && str_contains($value, '%')) {
                $query->where($key, 'like', $value);
            } else {
                $query->where($key, $value);
            }
        }
    }

    protected function between(Builder|QueryBuilder $query, $key, array $value)
    {
        $query->whereBetween($key, $value);
    }

    /**
     * Retrieve all records with given filter criteria
     *
     * @param  array  $search
     * @param  int|null  $limit
     * @param  string|null  $sortColumn
     * @param  string|null  $sortOrder
     * @param  array  $comparisonCriteria
     * @param  array  $with
     * @param  array  $columns
     * @param  array  $withCount
     * @param  null  $customSearchCriteria
     * @return LengthAwarePaginator|Builder[]|Collection
     */
    public function all(
        $search = [],
        $limit = null,
        $sortColumn = null,
        $sortOrder = null,
        $comparisonCriteria = [],
        $with = [],
        $columns = ['*'],
        $withCount = [],
        $customSearchCriteria = null
    ) {
        $query = $this->allQuery(
            $search,
            $columns,
            $limit,
            $sortColumn,
            $sortOrder,
            $comparisonCriteria,
            $customSearchCriteria
        )->with($with)->withCount($withCount);

        return $query->get($columns);
    }

    /**
     * Perform a search for generic index API routes with pagination support.
     *
     * @param  array  $search
     * @param  int|null  $limit
     * @param  string|null  $sortColumn
     * @param  string|null  $sortOrder
     * @param  array  $comparisonCriteria
     * @param  array  $with
     * @param  array  $columns
     * @param  array  $withCount
     * @param  null  $customSearchCriteria
     */
    public function search(
        $search = [],
        $limit = null,
        $sortColumn = null,
        $sortOrder = null,
        $comparisonCriteria = [],
        $with = [],
        $columns = ['*'],
        $withCount = [],
        $customSearchCriteria = null
    ): array {
        $metadata = [];

        if (request()->get('page') !== null) {
            $results = $this->allQuery(
                $search,
                $columns,
                null, // limit
                $sortColumn,
                $sortOrder,
                $comparisonCriteria,
                $customSearchCriteria
            )
                ->with($with)->withCount($withCount)->paginate($limit, $columns);
            $metadata = $results->toArray();

            unset($metadata['data']);
        } else {
            $results = $this->all(
                $search,
                $limit,
                $sortColumn,
                $sortOrder,
                $comparisonCriteria,
                $with,
                $columns,
                $withCount,
                $customSearchCriteria
            );
        }

        return ['models' => $results, 'metadata' => $metadata];
    }

    /**
     * Retrieve and paginate all records.
     *
     * @param  array  $search
     * @param  null  $limit
     * @param  string|null  $sortColumn
     * @param  string|null  $sortOrder
     * @param  array  $columns
     * @return LengthAwarePaginator
     */
    public function paginate($search = [], $limit = null, $sortColumn = null, $sortOrder = null, $columns = ['*'])
    {
        $query = $this->allQuery($search, $columns, null, $sortColumn, $sortOrder, $columns);

        return $query->paginate($limit, $columns);
    }

    /**
     * Create model record
     *
     * @param  array  $nonMassAssignableColumns
     * @return Model
     */
    public function create(array $input, $nonMassAssignableColumns = [])
    {
        $input = Arr::only($input, $this->model->getFillable());
        $model = $this->model->newInstance($input);
        foreach ($nonMassAssignableColumns as $column => $value) {
            $model->$column = $value;
        }

        $model->save();

        return $model;
    }

    /**
     * Find model record for given id
     *
     * @param  int  $id
     * @param  array  $columns
     * @return Builder|Builder[]|Collection|Model|null
     */
    public function find($id, $columns = ['*'])
    {
        $query = $this->model->query();

        return $query->find($id, $columns);
    }

    /**
     * @param  array  $params  key value of query
     * @return Builder|Builder[]|Collection|Model|null
     */
    public function getByFields($params = [])
    {
        $query = $this->model->query();

        return $query->where($params)->first();
    }

    /**
     * Update model record for given id
     *
     * @param  array  $input
     * @param  int  $id
     * @return Builder|Builder[]|Collection|Model
     */
    public function update($input, $id)
    {
        $input = Arr::only($input, $this->model->getFillable());
        $query = $this->model->query();

        $model = $query->findOrFail($id);

        $model->fill($input);

        $model->save();

        return $model;
    }

    /**
     * @param  int  $id
     * @return bool|mixed|null
     *
     * @throws Exception
     */
    public function delete($id)
    {
        $query = $this->model->query();

        $model = $query->findOrFail($id);

        return $model->delete();
    }

    /**
     * Handle dynamic method calls into the model.
     *
     * @param  string  $method
     * @param  array  $parameters
     * @return mixed
     */
    public function __call($method, $parameters)
    {
        if (!empty($this->excludedGlobalScopes)) {
            $model = $this->model->query()->withoutGlobalScopes($this->excludedGlobalScopes);
        } else {
            $model = $this->model->query();
        }

        return $this->forwardCallTo($model, $method, $parameters);
    }

    public function excludeGlobalScopes($exclude = false)
    {
        if ($exclude && !empty($this->excludedGlobalScopes)) {
            return $this->model->query()->withoutGlobalScopes($this->excludedGlobalScopes);
        }

        return $this->model->query();
    }

    /**
     * @return mixed
     */
    public function findByName($name)
    {
        return $this->where('name', $name)->first();
    }

    /**
     * @return mixed
     */
    public function findByColumn($column, $value)
    {
        return $this->where($column, $value)->first();
    }

    /**
     * @param  array  $additionalColumns
     * @return mixed
     */
    public function distinctResults($query, $additionalColumns = [])
    {
        $tableName = $this->model->getTable();

        $additionalColumns = $additionalColumns ? $additionalColumns :
            [
                "$tableName.id",
                "$tableName.created_at",
                "$tableName.updated_at",
                "$tableName.deleted_at",
            ];

        $fillables = $this->model->getFillable();

        $groupColumns = [];
        foreach ($fillables as $value) {
            $groupColumns[] = "$tableName.$value";
        }
        $groupColumns = array_merge($groupColumns, $additionalColumns);

        return $query->groupBy($groupColumns);
    }

    public function getModelByGuid(string $guid)
    {
        return $this->model->query()->where('guid', $guid)->firstOrFail();
    }

    protected function getLastUpdateQuery(array $fields)
    {
        $implodeFields = implode(',', array_map(
            function ($field) {
                return sprintf('MAX(COALESCE(%s.updated_at,null))', $field);
            },
            $fields
        ));

        return sprintf('GREATEST(%s) as last_updated_at', $implodeFields);
    }
}
