<?php

namespace App\Repositories;

use App\Common\Traits\Caching;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

class UserRepository extends BaseRepository
{
    use Caching;

    protected $modelClass = User::class;

    /**
     * We add custom search criteria here as we are including data from various
     * related models in the output.
     */
    protected function addSearchCriteria(array $search, Builder $query, array $comparisonCriteria)
    {
        $query->withoutGlobalScopes()
            ->select('users.*');

        $searchText = Arr::pull($search, 'search');

        if ($searchText) {
            $query->where(function (Builder $query) use ($searchText) {
                $query->orWhere('email', 'LIKE', "%{$searchText}%")
                    ->orWhere('phone_number', 'LIKE', "%{$searchText}%")
                    ->orWhere('first_name', 'LIKE', "%{$searchText}%")
                    ->orWhere('last_name', 'LIKE', "%{$searchText}%");
            });
        }

        parent::addSearchCriteria($search, $query, $comparisonCriteria);
    }

    protected function applySortOrder(
        Builder $query,
        $sortColumn = null,
        $sortOrder = null,
    ) {
        if ($sortColumn === 'role') {
            return $query->leftJoin('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
                ->leftJoin('roles', 'roles.id', '=', 'model_has_roles.role_id')
                ->orderBy('roles.name', $sortOrder);
        }

        if ($sortColumn === 'created_by') {
            return $query->leftJoin('users as creators', 'users.created_by', '=', 'creators.id')
                ->orderBy('creators.full_name', $sortOrder);
        }

        parent::applySortOrder($query, $sortColumn, $sortOrder);
    }

    /**
     * Find user by email
     */
    public function findByEmail(string $email): ?Model
    {
        return $this->model->withoutGlobalScopes()->where('email', $email)->first();
    }

    /**
     * Find user by phone
     */
    public function findByPhone(string $phone): ?Model
    {
        return $this->model->where('phone_number', $phone)->first();
    }

    /**
     * Check if email exists
     */
    public function emailExists(string $email, ?string $excludeId = null): bool
    {
        $query = $this->model->where('email', $email);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Check if phone exists
     */
    public function phoneExists(string $phone, ?string $excludeId = null): bool
    {
        $query = $this->model->where('phone_number', $phone);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Update user
     */
    public function updateUser(User $user, array $data): User
    {
        $user->update($data);

        return $user->fresh();
    }
}
