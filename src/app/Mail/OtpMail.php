<?php

declare(strict_types=1);

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class OtpMail extends Mailable
{
    use Queueable, SerializesModels;

    public array $mailData;

    /**
     * Create a new message instance.
     */
    public function __construct(array $mailData)
    {
        $this->mailData = $mailData;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: $this->mailData['subject'] ?? 'Your OTP Code',
            tags: ['otp', $this->mailData['type'] ?? 'otp'],
            metadata: [
                'type' => $this->mailData['type'] ?? 'otp',
                'identifier' => $this->mailData['identifier'] ?? '',
            ],
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: $this->mailData['template'] ?? 'emails.otp',
            with: [
                'otp' => $this->mailData['otp'],
                'type' => $this->mailData['type'] ?? 'otp',
                'expiryMinutes' => $this->getExpiryMinutes(),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }

    /**
     * Get expiry minutes based on OTP type
     */
    private function getExpiryMinutes(): int
    {
        return match ($this->mailData['type']) {
            'password_reset' => 1440, // 24 hours
            'registration' => 3, // 3 minutes
            default => 3, // Default to 3 minutes for other types
        };
    }
}
