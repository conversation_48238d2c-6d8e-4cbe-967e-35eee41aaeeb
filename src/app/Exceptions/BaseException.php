<?php

namespace App\Exceptions;

use App\Helpers\Common;
use App\Http\ApiResponse;
use Exception;
use Illuminate\Http\Request;
use Throwable;

abstract class BaseException extends Exception
{
    protected string $messageKey = 'bad_request';

    protected ?string $detail = null;

    public function __construct(?string $messageKey = null, $code = 400, $replace = [], ?Throwable $previous = null)
    {
        $messageKey = $messageKey ?? $this->messageKey;
        $apiMessage = Common::makeApiMessage("errors.{$messageKey}", $replace);

        $message = $apiMessage->hasMessageKey() ? $apiMessage->getLocalisedMessage() : $messageKey;
        $this->detail = $apiMessage->getDetailedMessage();

        parent::__construct($message, $code, $previous);
    }

    public function getDetailedMessage(): ?string
    {
        return $this->detail;
    }

    /**
     * Render the exception into an HTTP response.
     */
    public function render(Request $request): ApiResponse
    {
        $debug = config('app.debug');
        $status = $this->getCode() ?: 500;

        if ($debug) {
            $data = [
                'exception' => get_class($this),
                'file' => $this->getFile(),
                'line' => $this->getLine(),
            ];
        }

        return new ApiResponse(
            $this->getMessage(),
            false,
            $data ?? [],
            [],
            $status,
            [],
            $debug ? $this->getDetailedMessage() : null
        );
    }
}
