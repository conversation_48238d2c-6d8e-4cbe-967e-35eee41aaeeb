<?php

namespace App\Services;

use App\Models\Camp;
use App\Repositories\CampRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class CampService
{
    public function __construct(
        protected CampRepository $campRepository,
        protected CampActionLogger $campActionLogger
    ) {
    }

    /**
     * Get all camps.
     */
    public function getAll(): Collection
    {
        return $this->campRepository->all();
    }

    /**
     * Find a camp by ID.
     */
    public function getById(int $id): ?Camp
    {
        return $this->campRepository->find($id);
    }

    /**
     * Create a new camp.
     */
    public function create(array $data): Camp
    {
        $userId = $this->getUserIdOrFail();

        $data['created_by'] = $userId;
        $data['updated_by'] = $userId;

        $camp = $this->campRepository->create($data);

        $this->campActionLogger->log('created', $camp);

        return $camp;
    }

    /**
     * Update an existing camp.
     */
    public function update(Camp $camp, array $data): Camp
    {
        $data['updated_by'] = $this->getUserIdOrFail();

        $this->campRepository->update($camp, $data);

        $updatedCamp = $camp->fresh();

        $this->campActionLogger->log('updated', $updatedCamp);

        return $updatedCamp;
    }

    /**
     * Delete a camp by ID.
     */
    public function delete(Camp $camp): bool
    {
        $deleted = $this->campRepository->delete($camp);

        if ($deleted) {
            $this->campActionLogger->log('deleted', $camp);
        }

        return $deleted;
    }

    /**
     * Filter camps by conditions.
     */
    public function filterCamps(array $filters, int $perPage = 20): LengthAwarePaginator
    {
        return $this->campRepository->filter($filters, $perPage);
    }

    /**
     * Get authenticated user's UUID or throw exception.
     */
    protected function getUserIdOrFail(): string
    {
        $user = auth()->user();

        if (!$user) {
            throw new \Exception('Unauthorized: No authenticated user found.');
        }

        return $user->id;
    }

    /**
     * Duplicate a camp.
     * @throws \Exception
     */
    public function duplicate(array $data, Camp $camp): Camp
    {
        $userId = $this->getUserIdOrFail();

        $data['created_by'] = $userId;
        $data['updated_by'] = $userId;

        $newCamp = $this->campRepository->duplicate($camp, $data);

        $this->campActionLogger->log('duplicated', $newCamp);

        return $newCamp;
    }
}
