<?php

namespace App\Services;

use App\Helpers\CacheHelper;
use App\Helpers\Common;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Random\RandomException;

class OtpService
{
    public const OTP_LENGTH = 6;

    public const OTP_EXPIRY_MINUTES = 3;

    public const PASSWORD_RESET_OTP_EXPIRY_MINUTES = 1440; // 24 hours

    public const MAX_ATTEMPTS = 3;

    public const ATTEMPT_EXPIRY_MINUTES = 5;

    // OTP Types
    public const TYPE_REGISTER = 'register';

    public const TYPE_RESET = 'reset_password';

    /**
     * Generate OTP for phone number or email with type
     *
     * @throws ValidationException
     * @throws RandomException
     */
    public function generateOtp(string $identifier, string $type = self::TYPE_REGISTER): string
    {
        try {
            $this->checkRateLimit($identifier, $type);

            // Generate 6-digit OTP
            $otp = str_pad((string) random_int(0, 999999), self::OTP_LENGTH, '0', STR_PAD_LEFT);

            // Store OTP in Redis with expiry
            $cacheKey = $this->getOtpCacheKey($identifier, $type);
            $expiryMinutes = $type === self::TYPE_RESET
                ? self::PASSWORD_RESET_OTP_EXPIRY_MINUTES
                : self::OTP_EXPIRY_MINUTES;
            CacheHelper::set($cacheKey, $otp, $expiryMinutes * 60);

            $this->incrementAttempts($identifier, $type);

            $maskedIdentifier = $this->isEmail($identifier)
                ? Common::maskEmail($identifier)
                : Common::maskPhone($identifier);
            Log::info('OTP generated', [
                'identifier' => $maskedIdentifier,
                'type' => $type,
                'expires_in_minutes' => $expiryMinutes,
                'otp' => Common::isTestOrDevEnv() ? $otp : '******', // Mask OTP in production
            ]);

            return $otp;
        } catch (ValidationException $e) {
            $maskedIdentifier = $this->isEmail($identifier)
                ? Common::maskEmail($identifier)
                : Common::maskPhone($identifier);
            Log::warning('OTP generation rate limited', [
                'identifier' => $maskedIdentifier,
                'type' => $type,
                'attempts' => $this->getAttemptCount($identifier, $type),
            ]);

            throw $e;
        } catch (\Exception $e) {
            $maskedIdentifier = $this->isEmail($identifier)
                ? Common::maskEmail($identifier)
                : Common::maskPhone($identifier);
            Log::error('OTP generation failed', [
                'identifier' => $maskedIdentifier,
                'type' => $type,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Verify OTP for phone number or email with type
     *
     * @throws ValidationException
     */
    public function verifyOtp(string $identifier, string $otp, string $type = self::TYPE_REGISTER): bool
    {
        try {
            $cacheKey = $this->getOtpCacheKey($identifier, $type);
            $storedOtp = CacheHelper::get($cacheKey);

            if (!$storedOtp) {
                $maskedIdentifier = $this->isEmail($identifier)
                    ? Common::maskEmail($identifier)
                    : Common::maskPhone($identifier);
                Log::warning('OTP verification failed - expired', [
                    'identifier' => $maskedIdentifier,
                    'type' => $type,
                ]);

                $errorMessage = $type === self::TYPE_RESET
                    ? 'messages.invalid_expired_otp'
                    : 'validation.otp_expired_or_invalid';
                throw ValidationException::withMessages([
                    'otp' => [$errorMessage],
                ]);
            }

            if ($storedOtp !== $otp) {
                $maskedIdentifier = $this->isEmail($identifier)
                    ? Common::maskEmail($identifier)
                    : Common::maskPhone($identifier);
                Log::warning('OTP verification failed - invalid', [
                    'identifier' => $maskedIdentifier,
                    'type' => $type,
                ]);

                $errorMessage = $type === self::TYPE_RESET ? 'messages.invalid_expired_otp' : 'validation.otp_invalid';
                throw ValidationException::withMessages([
                    'otp' => [$errorMessage],
                ]);
            }

            // Clear OTP after successful verification
            CacheHelper::delete($cacheKey);

            $maskedIdentifier = $this->isEmail($identifier)
                ? Common::maskEmail($identifier)
                : Common::maskPhone($identifier);
            Log::info('OTP verified successfully', [
                'identifier' => $maskedIdentifier,
                'type' => $type,
            ]);

            return true;
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            $maskedIdentifier = $this->isEmail($identifier)
                ? Common::maskEmail($identifier)
                : Common::maskPhone($identifier);
            Log::error('OTP verification error', [
                'identifier' => $maskedIdentifier,
                'type' => $type,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Check if identifier is rate limited
     *
     * @throws ValidationException
     */
    private function checkRateLimit(string $identifier, string $type): void
    {
        $attemptKey = $this->getAttemptCacheKey($identifier, $type);
        $attempts = CacheHelper::get($attemptKey, 0);

        if ($attempts >= self::MAX_ATTEMPTS) {
            throw ValidationException::withMessages([
                $this->isEmail($identifier) ? 'email' : 'phone_number' => ['validation.otp_too_many_attempts'],
            ]);
        }
    }

    /**
     * Increment attempt counter
     */
    private function incrementAttempts(string $identifier, string $type): void
    {
        $attemptKey = $this->getAttemptCacheKey($identifier, $type);
        $attempts = CacheHelper::get($attemptKey, 0);

        CacheHelper::set($attemptKey, $attempts + 1, self::ATTEMPT_EXPIRY_MINUTES * 60);
    }

    /**
     * Get current attempt count for identifier
     */
    private function getAttemptCount(string $identifier, string $type): int
    {
        $attemptKey = $this->getAttemptCacheKey($identifier, $type);

        return CacheHelper::get($attemptKey, 0);
    }

    /**
     * Get OTP cache key based on type
     */
    private function getOtpCacheKey(string $identifier, string $type): string
    {
        $prefix = $type === self::TYPE_RESET ? 'password_reset_otp' : 'otp';

        return "{$prefix}:{$identifier}";
    }

    /**
     * Get attempt cache key based on type
     */
    private function getAttemptCacheKey(string $identifier, string $type): string
    {
        $prefix = $type === self::TYPE_RESET ? 'password_reset_otp_attempts' : 'otp_attempts';

        return "{$prefix}:{$identifier}";
    }

    /**
     * Clear OTP and attempts for identifier with type
     */
    public function clearOtp(string $identifier, string $type = self::TYPE_REGISTER): void
    {
        $otpKey = $this->getOtpCacheKey($identifier, $type);
        $attemptKey = $this->getAttemptCacheKey($identifier, $type);

        CacheHelper::delete($otpKey);
        CacheHelper::delete($attemptKey);
    }

    /**
     * Get remaining attempts for identifier with type
     */
    public function getRemainingAttempts(string $identifier, string $type = self::TYPE_REGISTER): int
    {
        $attemptKey = $this->getAttemptCacheKey($identifier, $type);
        $attempts = CacheHelper::get($attemptKey, 0);

        return max(0, self::MAX_ATTEMPTS - $attempts);
    }

    /**
     * Get OTP expiry time for identifier
     */
    public function getOtpExpiry(string $identifier, string $type = self::TYPE_REGISTER): ?string
    {
        $cacheKey = $this->getOtpCacheKey($identifier, $type);

        // Check if OTP exists
        if (!CacheHelper::has($cacheKey)) {
            return null;
        }

        // Return approximate expiry
        $expiryMinutes = $type === self::TYPE_RESET
            ? self::PASSWORD_RESET_OTP_EXPIRY_MINUTES
            : self::OTP_EXPIRY_MINUTES;
        return now()->addMinutes($expiryMinutes)->toISOString();
    }

    /**
     * Check if identifier is an email
     */
    private function isEmail(string $identifier): bool
    {
        return filter_var($identifier, FILTER_VALIDATE_EMAIL) !== false;
    }
}
