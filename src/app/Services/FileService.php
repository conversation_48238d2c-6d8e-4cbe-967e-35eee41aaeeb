<?php

namespace App\Services;

use App\Helpers\FileHelper;
use App\Models\File;
use App\Repositories\FileRepository;
use Exception;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class FileService
{
    const string GLOBAL = 'global';

    public function __construct(private readonly FileRepository $repository)
    {
    }

    public function get($request): array
    {
        return $this->repository->search(
            $request->getFilterColumns(),
            $request->get('limit'),
            $request->get('sort_column'),
            $request->get('sort_order'),
            $request->getFilterColumnComparisonCriteria(),
        );
    }

    /**
     * @return Model
     *
     * @throws FileNotFoundException
     */
    public function store(array $params, ?Model $fileable = null)
    {
        Log::info('Creating new file', [
            'fileable_type' => $fileable ? $fileable::class : 'global',
            'fileable_id' => $fileable ? $fileable->getKey() : null,
            'type' => $params['type'] ?? 'unknown',
            'original_name' => $params['file']->getClientOriginalName(),
            'size' => $params['file']->getSize(),
        ]);

        try {
            $file = $this->createFile($params, $fileable);

            Log::info('File created successfully', [
                'file_id' => $file->id,
                'path' => $file->path,
                'size' => $file->size,
            ]);

            return $file;
        } catch (Exception $e) {
            Log::error('Failed to create file', [
                'error' => $e->getMessage(),
                'fileable_type' => $fileable ? $fileable::class : 'global',
                'fileable_id' => $fileable ? $fileable->getKey() : null,
            ]);
            throw $e;
        }
    }

    /**
     * @return Model
     *
     * @throws FileNotFoundException
     * @throws Exception
     */
    private function createFile(array $params, ?Model $fileable = null)
    {
        $model = $fileable ? $fileable->getTable() : self::GLOBAL;

        $fileTempData = app(FileHelper::class)
            ->initialFileTempData(
                $params['file'],
                $model,
                $params['type'],
                $params['mime_type'] ?? null,
            );

        // Add polymorphic fields if fileable is provided
        if ($fileable) {
            $fileTempData['fileable_id'] = (string) $fileable->getKey();
            $fileTempData['fileable_type'] = $fileable::class;
        }

        Log::debug('Creating file record in database', [
            'temp_path' => $fileTempData['path'],
            'size' => $fileTempData['size'],
            'mime_type' => $fileTempData['mime_type'],
        ]);

        // Store file data
        $file = $this->repository->create($fileTempData);

        // Get temp path and upload temp file to S3
        $tempPath = $fileTempData['path'];

        Log::info('Uploading file to S3', [
            'file_id' => $file->id,
            'temp_path' => $tempPath,
            'mime_type' => $fileTempData['mime_type'],
        ]);

        $fileS3Info = app(FileHelper::class)->putTempFileToS3($tempPath, $fileTempData['mime_type']);

        Log::info('File uploaded to S3 successfully', [
            'file_id' => $file->id,
            's3_path' => $fileS3Info['path'],
            'etag' => $fileS3Info['etag'],
        ]);

        // Update etag and path of file from S3
        $file->update([
            'etag' => $fileS3Info['etag'],
            'path' => $fileS3Info['path'],
        ]);

        return $file;
    }

    /**
     * Update a file's metadata or replace the file.
     *
     * @throws Exception
     */
    public function update(File $file, array $params): File
    {
        Log::info('Updating file', [
            'file_id' => $file->id,
            'current_path' => $file->path,
            'has_new_file' => !empty($params['file']),
            'update_fields' => array_keys(array_intersect_key($params, array_flip(['type', 'mime_type', 'size', 'sha256_hash', 'etag', 'path']))),
        ]);

        // If a new file is uploaded, replace the file contents and update metadata
        if (!empty($params['file'])) {
            $model = $file->fileable ? $file->fileable->getTable() : self::GLOBAL;

            Log::info('Replacing file content', [
                'file_id' => $file->id,
                'model' => $model,
                'new_type' => $params['type'] ?? $file->type,
                'new_size' => $params['file']->getSize(),
                'new_name' => $params['file']->getClientOriginalName(),
            ]);

            $fileTempData = app(FileHelper::class)
                ->initialFileTempData(
                    $params['file'],
                    $model,
                    $params['type'] ?? $file->type,
                    $params['mime_type'] ?? $file->mime_type,
                );

            // Delete the old file from S3
            if ($file->path) {
                Log::info('Deleting old file from S3', [
                    'file_id' => $file->id,
                    'old_path' => $file->path,
                ]);

                $deleted = app(FileHelper::class)->deleteS3FileByLivePath($file->path);

                if (!$deleted) {
                    Log::warning('Failed to delete old file from S3', [
                        'file_id' => $file->id,
                        'old_path' => $file->path,
                    ]);
                }
            }

            // Upload new file to S3
            $fileS3Info = app(FileHelper::class)->putTempFileToS3($fileTempData['path'], $fileTempData['mime_type']);

            Log::info('New file uploaded to S3', [
                'file_id' => $file->id,
                'new_s3_path' => $fileS3Info['path'],
                'new_etag' => $fileS3Info['etag'],
            ]);

            // Update file fields
            $file->update([
                'type' => $fileTempData['type'],
                'size' => $fileTempData['size'],
                'sha256_hash' => $fileTempData['sha256_hash'],
                'mime_type' => $fileTempData['mime_type'],
                'etag' => $fileS3Info['etag'],
                'path' => $fileS3Info['path'],
            ]);
        } else {
            // Only update metadata fields
            $updateFields = array_intersect_key($params, array_flip(['type', 'mime_type', 'size', 'sha256_hash', 'etag', 'path']));

            Log::info('Updating file metadata only', [
                'file_id' => $file->id,
                'update_fields' => array_keys($updateFields),
            ]);

            $file->update($updateFields);
        }

        return $file->fresh();
    }

    /**
     * Delete a file record and optionally the S3 file.
     *
     * @throws Exception
     */
    public function delete(File $file): void
    {
        Log::info('Deleting file', [
            'file_id' => $file->id,
            'path' => $file->path,
            'fileable_type' => $file->fileable_type,
            'fileable_id' => $file->fileable_id,
            'type' => $file->type,
            'size' => $file->size,
        ]);

        // Delete the file from S3
        if ($file->path) {
            $deleted = app(FileHelper::class)->deleteS3FileByLivePath($file->path);

            if ($deleted) {
                Log::info('File deleted from S3 successfully', [
                    'file_id' => $file->id,
                    'path' => $file->path,
                ]);
            } else {
                Log::warning('Failed to delete file from S3', [
                    'file_id' => $file->id,
                    'path' => $file->path,
                ]);
            }
        }

        $file->delete();

        Log::info('File record deleted from database', [
            'file_id' => $file->id,
        ]);
    }
}
