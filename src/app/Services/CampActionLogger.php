<?php

namespace App\Services;

use App\Models\Camp;
use Illuminate\Support\Facades\Log;

class CampActionLogger
{
    public function log(string $action, Camp $camp): void
    {
        Log::info("Camp {$action}", [
            'user_id'    => auth()->id(),
            'camp_id'    => $camp->id,
            'camp_name'  => $camp->name,
            'timestamp'  => now()->toDateTimeString(),
        ]);
    }
}
