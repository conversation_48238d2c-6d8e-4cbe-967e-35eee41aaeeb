<?php

declare(strict_types=1);

namespace App\Services;

use App\Const\Common as CommonConst;
use App\Helpers\CacheHelper;
use App\Helpers\Common;
use App\Models\RefreshToken;
use App\Models\User;
use App\Repositories\UserRepository;
use App\Channels\EmailChannel;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class AuthService
{
    const TOKEN_EXPIRY_MINUTES = 10;

    public function __construct(
        private readonly UserRepository $userRepository,
        private readonly OtpService $otpService,
        private readonly EmailChannel $emailChannel
    ) {
    }
    /**
     * Login user with email and password
     *
     * @throws ValidationException
     */
    public function login(array $credentials): array
    {
        $user = $this->userRepository->findByEmail($credentials['email']);

        if (!$user) {
            throw ValidationException::withMessages([
                'email' => ['account_not_found'],
            ]);
        }

        if (!$user->isActive()) {
            throw ValidationException::withMessages([
                'email' => ['account_deactivated_contact_support'],
            ]);
        }

        if (!Hash::check($credentials['password'], $user->password)) {
            throw ValidationException::withMessages([
                'password' => ['incorrect_credentials'],
            ]);
        }

        $accessTokenObject = $user->createToken('api-token');
        $accessToken = $accessTokenObject->plainTextToken;
        $refreshToken = $this->generateRefreshToken($user, $accessTokenObject->accessToken->id);

        return [
            'user' => $user->toArray(),
            'access_token' => $accessToken,
            'refresh_token' => $refreshToken,
            'token_type' => 'Bearer',
        ];
    }

    /**
     * Generate temporary token for password setup
     */
    private function generateToken(string $email, string $type = 'password_setup'): string
    {
        $token = Str::random(64);
        $key = "{$type}:{$email}";

        // Store token in cache for 10 minutes
        CacheHelper::set($key, $token, self::TOKEN_EXPIRY_MINUTES * 60);

        return $token;
    }

    /**
     * Logout user (single device only)
     */
    public function logout(User $user): void
    {
        $token = $user->currentAccessToken();
        if ($token) {
            $token->delete();
        }
    }

    /**
     * Get user profile
     */
    public function getUserProfile(User $user): array
    {
        return [
            'user' => $this->formatUserData($user),
        ];
    }

    /**
     * Update user profile
     *
     * @throws ValidationException
     */
    public function updateProfile(User $user, array $updateData): array
    {
        if (isset($updateData['email']) &&
            $this->userRepository->emailExists($updateData['email'], $user->id)) {
            throw ValidationException::withMessages([
                'email' => ['validation.email_already_taken'],
            ]);
        }

        if (isset($updateData['phone_number']) &&
            $this->userRepository->phoneExists($updateData['phone_number'], $user->id)) {
            throw ValidationException::withMessages([
                'phone_number' => ['validation.phone_already_taken'],
            ]);
        }

        if (isset($updateData['password'])) {
            $updateData['password'] = Hash::make($updateData['password']);
        }

        $user->update($updateData);
        $user->refresh();

        return [
            'user' => $this->formatUserData($user),
        ];
    }

    /**
     * Refresh access token using refresh token
     *
     * @throws ValidationException
     */
    public function refreshToken(string $refreshToken): array
    {
        // Get access token from Authorization header to extract ID
        $accessToken = request()->bearerToken();

        if (!$accessToken) {
            throw ValidationException::withMessages([
                'refresh_token' => ['validation.refresh_token_required'],
            ]);
        }

        // Parse access token ID from format: "id|token"
        $tokenParts = explode('|', $accessToken, 2);
        if (count($tokenParts) !== 2) {
            throw ValidationException::withMessages([
                'refresh_token' => ['validation.refresh_token_invalid_format'],
            ]);
        }

        $accessTokenId = (int)$tokenParts[0];

        // Find refresh token by access token ID (O(1) lookup!)
        $tokenModel = RefreshToken::findByAccessTokenId($accessTokenId);

        if (!$tokenModel) {
            throw ValidationException::withMessages([
                'refresh_token' => ['validation.refresh_token_not_found'],
            ]);
        }

        // Verify the refresh token hash
        if (!Hash::check($refreshToken, $tokenModel->token)) {
            Log::warning('Token refresh failed - invalid refresh token', [
                'user_id' => $tokenModel->user_id,
                'ip' => request()?->ip() ?? 'unknown',
            ]);

            throw ValidationException::withMessages([
                'refresh_token' => ['validation.refresh_token_invalid'],
            ]);
        }

        $user = $tokenModel->user;

        if (!$user || !$user->isActive()) {
            throw ValidationException::withMessages([
                'refresh_token' => ['validation.user_account_deactivated'],
            ]);
        }

        // Revoke the old refresh token (token rotation)
        $tokenModel->revoke();

        // Generate new tokens
        $accessTokenObject = $user->createToken('api-token');
        $accessToken = $accessTokenObject->plainTextToken;
        $newRefreshToken = $this->generateRefreshToken($user, $accessTokenObject->accessToken->id);

        return [
            'user' => $this->formatUserData($user),
            'access_token' => $accessToken,
            'refresh_token' => $newRefreshToken,
            'token_type' => 'Bearer',
        ];
    }

    /**
     * Generate a refresh token for a user
     */
    private function generateRefreshToken(User $user, int $accessTokenId, ?string $deviceInfo = null): string
    {
        // Generate a random token
        $plainTextToken = Str::random(80);

        // Create refresh token linked to access token
        RefreshToken::create([
            'user_id' => $user->id,
            'access_token_id' => $accessTokenId,
            'token' => Hash::make($plainTextToken),
            'device_info' => $deviceInfo,
            'expires_at' => now()->addDays(RefreshToken::DEFAULT_EXPIRY_DAYS),
        ]);

        return $plainTextToken;
    }

    /**
     * Format user data for API response
     */
    private function formatUserData(User $user): array
    {
        $userData = $user->toArray();

        $userData['email'] = $user->email;
        $userData['first_name'] = $user->first_name;
        $userData['phone_number'] = $user->phone_number;
        $userData['last_name'] = $user->last_name;
        if ($user->date_of_birth) {
            $userData['date_of_birth'] = $user->date_of_birth?->format('Y-m-d');
        }
        $userData['email_verified_at'] = $user->email_verified_at?->format(CommonConst::DATETIME_FORMAT);

        return $userData;
    }

    /**
     * Initiate forgot password process - send OTP to registered email
     *
     * @throws ValidationException
     */
    public function initiateForgotPassword(string $email): array
    {
        $user = $this->userRepository->findByEmail($email);

        if (!$user) {
            Log::warning('Forgot password failed - email not found', [
                'email' => $email,
            ]);

            throw ValidationException::withMessages([
                'email' => ['email_not_found_sign_up'],
            ]);
        }

        if (!$user->isActive()) {
            throw ValidationException::withMessages([
                'email' => ['account_deactivated_contact_support'],
            ]);
        }

        $otp = $this->otpService->generateOtp($email, OtpService::TYPE_RESET);

        $emailSent = $this->emailChannel->sendPasswordResetOtp($email, $otp);

        if (!$emailSent) {
            Log::error('Failed to send password reset OTP email', [
                'email' => $email,
                'user_id' => $user->id,
            ]);

            throw ValidationException::withMessages([
                'email' => ['failed_to_send_otp'],
            ]);
        }

        $result = [
            'email' => $email,
            'expires_in' => OtpService::PASSWORD_RESET_OTP_EXPIRY_MINUTES * 60, // 24 hours in seconds
            'remaining_attempts' => $this->otpService->getRemainingAttempts($email, OtpService::TYPE_RESET),
        ];

        if (config('app.debug') && Common::isTestOrDevEnv()) {
            $result['otp'] = $otp;
        }

        return $result;
    }

    /**
     * Verify OTP for password reset and return reset token
     *
     * @throws ValidationException
     */
    public function verifyPasswordResetOtp(string $email, string $otp): array
    {
        $this->otpService->verifyOtp($email, $otp, OtpService::TYPE_RESET);

        $user = $this->userRepository->findByEmail($email);

        if (!$user) {
            throw ValidationException::withMessages([
                'email' => ['email_not_found_sign_up'],
            ]);
        }

        if (!$user->isActive()) {
            throw ValidationException::withMessages([
                'email' => ['account_deactivated_contact_support'],
            ]);
        }

        $resetToken = $this->generateToken($email, 'password_reset_token');

        return [
            'email' => $email,
            'verified' => true,
            'reset_token' => $resetToken,
            'reset_token_expires_in' => 600, // 10 minutes
        ];
    }

    /**
     * Reset password using reset token and email validation
     *
     * @throws ValidationException
     */
    public function resetPassword(string $email, string $newPassword, string $token): array
    {
        $cacheKey = "password_reset_token:{$email}";
        $storedToken = CacheHelper::get($cacheKey);

        if ($storedToken !== $token) {
            throw ValidationException::withMessages([
                'reset_token' => ['invalid_reset_token'],
            ]);
        }

        $user = $this->userRepository->findByEmail($email);

        if (!$user) {
            throw ValidationException::withMessages([
                'email' => ['email_not_found_sign_up'],
            ]);
        }

        if (!$user->isActive()) {
            throw ValidationException::withMessages([
                'email' => ['account_deactivated_contact_support'],
            ]);
        }

        $user->password = Hash::make($newPassword);
        $user->save();

        $this->revokeAllUserTokens($user);

        CacheHelper::delete($cacheKey);

        $this->otpService->clearOtp($email, OtpService::TYPE_RESET);

        Log::info('Password reset completed', [
            'email' => $email,
            'user_id' => $user->id,
        ]);

        return [
            'success' => true,
        ];
    }

    /**
     * Revoke all tokens for a user (access tokens and refresh tokens)
     */
    private function revokeAllUserTokens(User $user): void
    {
        $user->tokens()->delete();
    }

    /**
     * Update password for authenticated user
     *
     * @throws ValidationException
     */
    public function updatePassword(User $user, string $currentPassword, string $newPassword): array
    {
        if (!Hash::check($currentPassword, $user->password)) {
            throw ValidationException::withMessages([
                'current_password' => ['current_password'],
            ]);
        }

        $user->password = Hash::make($newPassword);
        $user->save();

        return [
            'success' => true,
        ];
    }

    /**
     * Attempt to authenticate admin user for admin panel
     *
     * @throws ValidationException
     */
    public function attemptPanelLogin(string $email, string $password, bool $remember = false): array
    {
        try {
            $user = $this->userRepository->findByEmail($email);

            if (!$user) {
                throw ValidationException::withMessages([
                    'email' => ['auth_service.invalid_credentials'],
                ]);
            }

            if (!Hash::check($password, $user->password)) {
                throw ValidationException::withMessages([
                    'email' => ['auth_service.invalid_credentials'],
                ]);
            }

            if (!$user->isActive()) {
                throw ValidationException::withMessages([
                    'email' => ['auth_service.account_deactivated_contact'],
                ]);
            }

            $credentials = [
                'email' => $email,
                'password' => $password,
            ];

            if (Auth::attempt($credentials, $remember)) {
                $user->updateLastLogin();

                return [
                    'success' => true,
                    'user' => $this->formatUserData($user),
                ];
            }

            throw ValidationException::withMessages([
                'email' => ['auth_service.invalid_credentials'],
            ]);
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Panel login failed', [
                'email' => $email,
                'error' => $e->getMessage(),
            ]);

            throw ValidationException::withMessages([
                'email' => ['unexpected_error'],
            ]);
        }
    }

    public function logoutAdmin(): void
    {
        $user = Auth::user();

        if ($user) {
            if (method_exists($user, 'tokens')) {
                $user->tokens()->delete();
            }

            if (method_exists($user, 'forgetCache')) {
                $user->forgetCache();
            }
        }

        Auth::logout();

        session()->forget(['admin_user', 'admin_permissions', 'admin_roles']);
    }

    public function getCurrentUser(): Authenticatable
    {
        return Auth::user();
    }
}
