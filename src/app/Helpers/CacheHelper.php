<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CacheHelper
{
    private const DEFAULT_STORE = 'database';

    private const DEFAULT_TTL = 300; // 5 minutes in seconds

    /**
     * Get value from cache
     */
    public static function get(string $key, $default = null)
    {
        try {
            return Cache::store(self::DEFAULT_STORE)->get($key, $default);
        } catch (\Exception $e) {
            Log::error('Failed to get cache value', [
                'key' => $key,
                'error' => $e->getMessage(),
            ]);

            return $default;
        }
    }

    /**
     * Set value in cache
     */
    public static function set(string $key, $value, int $ttl = self::DEFAULT_TTL): bool
    {
        try {
            return Cache::store(self::DEFAULT_STORE)->put($key, $value, $ttl);
        } catch (\Exception $e) {
            Log::error('Failed to set cache value', [
                'key' => $key,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Check if key exists in cache
     */
    public static function has(string $key): bool
    {
        try {
            return Cache::store(self::DEFAULT_STORE)->has($key);
        } catch (\Exception $e) {
            Log::error('Failed to check cache key', [
                'key' => $key,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Delete key from cache
     */
    public static function delete(string $key): bool
    {
        try {
            return Cache::store(self::DEFAULT_STORE)->forget($key);
        } catch (\Exception $e) {
            Log::error('Failed to delete cache key', [
                'key' => $key,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Increment counter in cache
     */
    public static function increment(string $key, int $value = 1): int
    {
        try {
            return Cache::store(self::DEFAULT_STORE)->increment($key, $value);
        } catch (\Exception $e) {
            Log::error('Failed to increment cache value', [
                'key' => $key,
                'error' => $e->getMessage(),
            ]);

            return 0;
        }
    }

    /**
     * Decrement counter in cache
     */
    public static function decrement(string $key, int $value = 1): int
    {
        try {
            return Cache::store(self::DEFAULT_STORE)->decrement($key, $value);
        } catch (\Exception $e) {
            Log::error('Failed to decrement cache value', [
                'key' => $key,
                'error' => $e->getMessage(),
            ]);

            return 0;
        }
    }

    /**
     * Clear all cache
     */
    public static function clear(): bool
    {
        try {
            return Cache::store(self::DEFAULT_STORE)->clear();
        } catch (\Exception $e) {
            Log::error('Failed to clear cache', [
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Get multiple values from cache
     */
    public static function getMultiple(array $keys): array
    {
        try {
            $results = [];
            foreach ($keys as $key) {
                $results[$key] = Cache::store(self::DEFAULT_STORE)->get($key);
            }

            return $results;
        } catch (\Exception $e) {
            Log::error('Failed to get multiple cache values', [
                'keys' => $keys,
                'error' => $e->getMessage(),
            ]);

            return array_fill_keys($keys, null);
        }
    }

    /**
     * Set multiple values in cache
     */
    public static function setMultiple(array $values, int $ttl = self::DEFAULT_TTL): bool
    {
        try {
            foreach ($values as $key => $value) {
                Cache::store(self::DEFAULT_STORE)->put($key, $value, $ttl);
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to set multiple cache values', [
                'values' => array_keys($values),
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Delete multiple keys from cache
     */
    public static function deleteMultiple(array $keys): bool
    {
        try {
            foreach ($keys as $key) {
                Cache::store(self::DEFAULT_STORE)->forget($key);
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to delete multiple cache keys', [
                'keys' => $keys,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Get cache store instance
     */
    public static function store()
    {
        return Cache::store(self::DEFAULT_STORE);
    }

    /**
     * Remember value in cache (get or set)
     */
    public static function remember(string $key, int $ttl, callable $callback)
    {
        try {
            return Cache::store(self::DEFAULT_STORE)->remember($key, $ttl, $callback);
        } catch (\Exception $e) {
            Log::error('Failed to remember cache value', [
                'key' => $key,
                'error' => $e->getMessage(),
            ]);

            return $callback();
        }
    }

    /**
     * Remember value in cache forever
     */
    public static function rememberForever(string $key, callable $callback)
    {
        try {
            return Cache::store(self::DEFAULT_STORE)->rememberForever($key, $callback);
        } catch (\Exception $e) {
            Log::error('Failed to remember cache value forever', [
                'key' => $key,
                'error' => $e->getMessage(),
            ]);

            return $callback();
        }
    }
}
