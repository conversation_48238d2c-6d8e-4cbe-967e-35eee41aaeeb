<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Lang;

class ApiMessages
{
    private string $message;

    private ?string $detail = null;

    private bool $hasMessageKey = false;

    private bool $hasDetailKey = false;

    public function __construct(string $translationKey, array $placeholders = [], string $translationSource = 'response-messages')
    {
        $this->message = Common::___($translationKey, $placeholders, $translationSource, 'en');
        $detail = Common::___($translationKey, $placeholders, 'detail', 'en');

        $this->hasMessageKey = Lang::has(self::getMessageKey($translationKey, $translationSource));

        /**
         * The default behaviour for the laravel __ helper is to return back
         * the translationKey if no translation is found.
         *
         * As we often do not have extra detail for messages we only apply the
         * result if we have a real translation.
         */
        if ($detail !== 'detail.'.$translationKey) {
            $this->hasDetailKey = true;
            $this->detail = $detail;
        }
    }

    public function getLocalisedMessage(): string
    {
        return $this->message;
    }

    public function getDetailedMessage(): ?string
    {
        return $this->detail;
    }

    public static function getMessageKey(string $translationKey, string $translationSource = 'response-messages'): ?string
    {
        return "{$translationSource}.{$translationKey}";
    }

    public function hasMessageKey(): bool
    {
        return $this->hasMessageKey;
    }

    public function hasDetailKey(): bool
    {
        return $this->hasDetailKey;
    }
}
