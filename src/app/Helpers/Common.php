<?php

namespace App\Helpers;

class Common
{
    /**
     * Translate the given message.
     */
    public static function ___(
        string $translationKey,
        array $replace = [],
        string $translationSource = 'response-messages',
        ?string $locale = null
    ): string {
        $translationKey = ApiMessages::getMessageKey($translationKey, $translationSource);

        return __($translationKey, $replace, $locale);
    }

    /**
     * Create API message object.
     */
    public static function makeApiMessage(
        string $translationKey,
        array $replace = [],
        string $translationSource = 'response-messages'
    ): ApiMessages {
        return new ApiMessages($translationKey, $replace, $translationSource);
    }

    /**
     * Check if the current environment is local, development or testing
     */
    public static function isTestOrDevEnv(): bool
    {
        $env = app()->environment();

        return in_array($env, ['local', 'dev', 'testing'], true);
    }

    /**
     * Mask phone number for logging (security consideration)
     * In test/dev environments, returns unmasked phone for debugging
     */
    public static function maskPhone(string $phone): string
    {
        // Don't mask in test/dev environments for easier debugging
        if (self::isTestOrDevEnv()) {
            return $phone;
        }

        // Mask phone in production environments
        if (strlen($phone) <= 4) {
            return str_repeat('*', strlen($phone));
        }

        return substr($phone, 0, 3) . str_repeat('*', strlen($phone) - 5) . substr($phone, -2);
    }

    /**
     * Mask email for logging (security consideration)
     * In test/dev environments, returns unmasked email for debugging
     */
    public static function maskEmail(string $email): string
    {
        // Don't mask in test/dev environments for easier debugging
        if (self::isTestOrDevEnv()) {
            return $email;
        }

        $parts = explode('@', $email);
        if (count($parts) !== 2) {
            return str_repeat('*', strlen($email));
        }

        $username = $parts[0];
        $domain = $parts[1];

        if (strlen($username) <= 2) {
            $maskedUsername = str_repeat('*', strlen($username));
        } else {
            $maskedUsername = substr($username, 0, 2) . str_repeat('*', strlen($username) - 2);
        }

        return $maskedUsername . '@' . $domain;
    }
}
