<?php

namespace App\Helpers;

use Aws\CommandPool;
use Aws\Exception\AwsException;
use Exception;
use Illuminate\Support\Facades\Log;

class S3Helper
{
    public $bucketURL;

    public $lastErrorMessage;

    public $lastAwsErrorMessage;

    public $lastAwsException;

    protected $bucketName;

    protected $bucketRegion;

    protected $bucketKey;

    protected $bucketSecret;

    protected $defaultAcl;

    protected $awsConfig;

    protected $sdk;

    protected $s3;

    protected $defaultBucketName;

    protected $statusConnect;

    const S3_DIRECTORY_TYPE = 'application/x-directory';

    /**
     * S3Helper constructor.
     *
     * @param  array  $configs
     */
    public function __construct($configs = [])
    {
        // Validate configs parameters
        $missing = [];
        foreach (['region', 'access_key', 'access_secret', 'bucket'] as $item) {
            if (!isset($configs[$item])) {
                $missing[] = $item;
            }
        }

        // Logging the missing config parameters and return false
        if (!empty($missing)) {
            Log::error(sprintf('S3 missing config field : %s', implode(', ', $missing)));

            return false;
        }

        $this->defaultBucketName = $configs['bucket'];
        $this->bucketRegion = $configs['region'];
        $this->bucketKey = $configs['access_key'];
        $this->bucketSecret = $configs['access_secret'];
        $this->defaultAcl = $configs['default_acl'] ?? null;

        // Construct bucket URL
        // Handle cases where bucket name is a fully qualified hostname
        $this->bucketURL = "https://" . $configs['bucket'];
        if (strpos($configs['bucket'], '.') === false) {
            $this->bucketURL .= ".s3.amazonaws.com/";
        }

        $this->awsConfig = [
            'region' => $this->bucketRegion,
            'version' => 'latest',
            'credentials' => [
                'key' => $this->bucketKey,
                'secret' => $this->bucketSecret,
            ],
        ];

        if (!empty($this->defaultAcl)) {
            $this->awsConfig['ACL'] = $this->defaultAcl;
        }

        $result = $this->connect();

        if (!$result) {
            Log::error('ERROR: Unable to connect to AWS S3');
        }

        return $result;
    }

    private function connect()
    {
        try {
            $this->sdk = new \Aws\Sdk($this->awsConfig);
            $this->s3 = $this->sdk->createS3();

            // Check connection to this Bucket is successful by
            // attempting to list a single object
            $checkConnectionCommand = $this->s3->getCommand('ListObjectsV2', [
                'Bucket' => $this->defaultBucketName,
                'Prefix' => '',
                'MaxKeys' => 1,
            ]);
            $this->s3->execute($checkConnectionCommand);
        } catch (AwsException $e) {
            $this->logAwsException($e);
            $this->setStatusConnect(false);

            return false;
        } catch (Exception $e) {
            $this->lastErrorMessage = $e->getMessage();
            $this->setStatusConnect(false);
            Log::error($e->getMessage());

            return false;
        }

        $this->setStatusConnect(true);

        return true;
    }

    /**
     * @param  array  $bucketName
     * @param  string|null  $contentType
     * @return bool
     *
     * @throws Exception
     */
    public function putObject($keyName, $body, $bucketName = [], $contentType = null)
    {
        if (empty($bucketName)) {
            $bucketName = $this->defaultBucketName;
        }

        try {
            $result = $this->s3->putObject([
                'Bucket' => $bucketName,
                'Key' => $keyName,
                'Body' => $body,
                'ContentType' => $contentType,
            ]);
        } catch (AwsException $e) {
            $this->logAwsException($e);
            throw new Exception(sprintf("AWS put object to bucket: %s  fail: %s", $bucketName, $this->lastErrorMessage));
        } catch (Exception $e) {
            $this->lastErrorMessage = $e->getMessage();
            throw new Exception(sprintf("AWS put object to bucket: %s  fail: %s", $bucketName, $this->lastErrorMessage));
        }

        return $result->toArray();
    }

    /*
     * Generate a list of buckets
     *
     */
    public function listBuckets()
    {
        $buckets = [];
        $result = false;

        try {
            $response = $this->s3->listBuckets();
        } catch (AwsException $e) {
            $this->logAwsException($e);

            return false;
        } catch (Exception $e) {
            $this->lastErrorMessage = $e->getMessage();
            Log::error($e->getMessage());

            return false;
        }

        if ($response instanceof \Aws\Result) {
            $buckets = $response['Buckets'];
            $result = true;
        }

        return [
            'result' => $result,
            'buckets' => $buckets,
        ];
    }

    /*
     * get Object Url
     *
     */
    public function getObjectUrl(string $key, $bucketName = null)
    {
        if (empty($bucketName)) {
            $bucketName = $this->defaultBucketName;
        }

        try {
            return $this->s3->getObjectUrl($bucketName, $key);
        } catch (AwsException $e) {
            $this->logAwsException($e);

            return false;
        } catch (Exception $e) {
            $this->lastErrorMessage = $e->getMessage();
            Log::error($e->getMessage());

            return false;
        }
    }

    /**
     * @param  null  $bucketName
     * @return bool
     */
    public function getObjectMetadata($objectKey, $bucketName = null)
    {
        if (empty($bucketName)) {
            $bucketName = $this->defaultBucketName;
        }

        $objectKey = ltrim($objectKey, '/');

        $command = $this->s3->getCommand(
            'headObject',
            [
                'Bucket' => $bucketName,
                'Key' => $objectKey,
            ]
        );

        try {
            $objectInfo = $this->s3->execute($command);
        } catch (AwsException $e) {
            $this->logAwsException($e);

            return false;
        } catch (Exception $e) {
            $this->lastErrorMessage = $e->getMessage();
            Log::error($e->getMessage());

            return false;
        }

        $arrayInfo = $objectInfo->toArray();

        return $arrayInfo['@metadata']['headers'];
    }

    /*
     * Check if an object key exists within a bucket
     * Suitable for files and directories however directories must include trailing slash
     *
     * @param $objectKey
     * @param null $bucketName
     * @return array
     */
    public function objectExists($objectKey, $bucketName = null)
    {
        if (empty($bucketName)) {
            $bucketName = $this->defaultBucketName;
        }

        // Root always exists
        if ($objectKey == '/') {
            return [
                'result' => true,
                'objectURL' => $this->bucketURL,
            ];
        }

        $objectKey = ltrim($objectKey, '/');

        $objectInfo = $this->s3->doesObjectExist($bucketName, $objectKey, []);

        if ($objectInfo) {
            return [
                'result' => true,
                'objectURL' => $this->bucketURL . '/' . $objectKey,
            ];
        }

        return [
            'result' => false,
            'objectURL' => null,
        ];
    }

    /*
     * Recursively copy one key (path) to another key within a bucket
     *
     * @param $srcPath
     * @param $dstPath
     * @param null $bucketName
     * @return array
     */
    public function copyPathRecursive($srcPath, $dstPath, $bucketName = null)
    {
        if (empty($bucketName)) {
            $bucketName = $this->defaultBucketName;
        }

        // Strip the leading slash and ensure a trailing slash is present
        $srcPath = trim($srcPath, '/') . '/';
        $dstPath = trim($dstPath, '/') . '/';

        $batch = [];
        $batch_desc = [];

        // Iterate the objects in the source path and prepare a batch of
        // CopyObject operations to copy all objects to destination path
        // The use of getIterator here gets around the normal limit of 1000
        // objects per ListObjects request. This code will list all objects
        // without limit.
        $iterator = $this->getListObjectsByIteratorCommand($bucketName, $srcPath);

        foreach ($iterator as $object) {
            // Create the target key by taking the source key and removing source path
            $targetKey = str_replace($srcPath, '', $object['Key']);

            if (!empty($targetKey)) {
                $sourceKey = $bucketName . '/' . $object['Key'];
                $targetKey = $dstPath . $targetKey;

                $batch_desc[] = [
                    'CopySource' => $sourceKey,
                    'Key' => $targetKey,
                ];

                $batch[] = $this->s3->getCommand('CopyObject', [
                    'Bucket' => $bucketName,
                    'CopySource' => $sourceKey,
                    'Key' => $targetKey,
                    'MetadataDirective' => 'COPY',
                ]);
            }
        }

        // Execute the command batch
        $objects = [];
        $exceptions = [];
        try {
            $responses = CommandPool::batch($this->s3, $batch);
            foreach ($responses as $response) {
                if ($response instanceof \Aws\Result) {
                    $objects[] = $response['ObjectURL'];
                }
                if ($response instanceof AwsException) {
                    $exceptions[] = $response;
                }
            }
        } catch (Exception $e) {
            Log::error(sprintf("ERROR in %s(): %s", __FUNCTION__, $e->getMessage()));
            throw new Exception(sprintf("AWS copy path recursive to bucket: %s  fail: %s", $bucketName, $e->getMessage()));
        }

        return [
            'result' => true,
            'objectURLs' => $objects,
            'objectCount' => count($objects),
            'exceptions' => $exceptions,
            'exceptionCount' => count($exceptions),
        ];
    }

    /*
     * Perform a batch of copy operations within a bucket
     *
     * Note: Target is treated as a directory path if trailing slash is present
     *
     *  $result = $s3Helper->copyObjects([
     *      [
     *          'source' => 'test1/file',
     *          'target' => 'test6/',
     *      ],
     *  ]);
     *
     *  $result = $s3Helper->copyObjects([
     *      [ 'test1/file1', 'test7/' ],
     *      [ 'test1/file2', 'test8/file2' ],
     *  ]);
     *
     * @param $sourceTargetPairs
     * @param null $sourceBucket
     * @param null $targetBucket
     * @return array
     */
    public function copyObjects($sourceTargetPairs, $sourceBucket = null, $targetBucket = null)
    {
        if (empty($targetBucket)) {
            $targetBucket = $this->defaultBucketName;
        }

        if (empty($sourceBucket)) {
            $sourceBucket = $this->defaultBucketName;
        }

        $batch = [];
        $batch_desc = [];

        foreach ($sourceTargetPairs as $sourceTargetPair) {
            if (array_key_exists('source', $sourceTargetPair) &&
                array_key_exists('target', $sourceTargetPair)) {
                $sourceIndex = 'source';
                $targetIndex = 'target';
            } elseif (count($sourceTargetPair) == 2 &&
                array_key_exists(0, $sourceTargetPair) &&
                array_key_exists(1, $sourceTargetPair)) {
                $sourceIndex = 0;
                $targetIndex = 1;
            } else {
                $batch_desc[] = [
                    'objectError' => 'Invalid source target pair',
                ];

                continue;
            }

            $sourceKey = $sourceBucket . '/' . ltrim($sourceTargetPair[$sourceIndex], '/');
            $targetKey = ltrim($sourceTargetPair[$targetIndex], '/');

            // Append object name if target is a directory
            if (substr($targetKey, -1) == '/') {
                $targetKey .= basename($sourceKey);
            }

            $batch_desc[] = [
                'CopySource' => $sourceKey,
                'Key' => $targetKey,
            ];

            $batch[] = $this->s3->getCommand('CopyObject', [
                'Bucket' => $targetBucket,
                'CopySource' => $sourceKey,
                'Key' => $targetKey,
                'MetadataDirective' => 'COPY',
            ]);
        }

        // Execute the command batch
        $objects = [];
        $exceptions = [];
        try {
            $responses = CommandPool::batch($this->s3, $batch);

            for ($i = 0; $i < count($responses); $i++) {
                $response = $responses[$i];
                if ($response instanceof \Aws\Result) {
                    $objects[] = $response['ObjectURL'];
                }
                if ($response instanceof AwsException) {
                    $errorDesc = sprintf("%s %s: %s - %s", $response->getAwsErrorType(), $response->getStatusCode(), $response->getAwsErrorCode(), $response->getAwsErrorMessage());
                    $this->logAwsException($response);

                    $exceptions[] = $i;
                    $batch_desc[$i]['exception'] = $errorDesc;
                }
            }
        } catch (Exception $e) {
            $errorDesc = $e->getMessage();
            Log::error(sprintf("ERROR in %s(): %s", __FUNCTION__, $errorDesc));

            $batch_desc[$i]['exception'] = $errorDesc;
        }

        return [
            'result' => true,
            'objectURLs' => $objects,
            'objectCount' => count($objects),
            'exceptions' => $exceptions,
            'exceptionCount' => count($exceptions),
            'batchDescription' => $batch_desc,
        ];
    }

    public function copyObject($srcFile, $dstPath, $srcBucket = null, $dstBucket = null)
    {
        if (empty($srcBucket)) {
            $srcBucket = $this->defaultBucketName;
        }
        if (empty($dstBucket)) {
            $dstBucket = $this->defaultBucketName;
        }

        // Strip the leading slash
        $srcFile = ltrim($srcFile, '/');
        $dstPath = trim($dstPath, '/');

        try {
            $sourceKey = $srcBucket . "/" . $srcFile;
            $targetKey = $dstPath . '/' . basename($srcFile);

            $response = $this->s3->copyObject([
                'Bucket' => $dstBucket,
                'CopySource' => $sourceKey,
                'Key' => $targetKey,
            ]);
        } catch (Exception $e) {
            $errorDesc = sprintf("ERROR in %s(): %s", __FUNCTION__, $e->getMessage());
            Log::error($errorDesc);
        }

        if (!empty($response) && $response instanceof \Aws\Result) {
            return [
                'result' => true,
                'objectURL' => $response['ObjectURL'],
            ];
        }

        return [
            'result' => false,
            'error' => $errorDesc,
        ];
    }

    /*
     * List all objects in a given path
     *
     */
    public function listObjects($path = '/', $bucketName = null, $maxKeys = null, string $startAfter = '')
    {
        $result = false;
        $objects = [];

        if (empty($bucketName)) {
            $bucketName = $this->defaultBucketName;
        }

        try {
            // Strip the leading slash and ensure a trailing slash is present
            $path = trim($path, '/') . '/';

            // Iterate the objects in the path
            $params = [
                'Bucket' => $bucketName,
                'Prefix' => $path,
            ];
            if (!empty($startAfter)) {
                $params['StartAfter'] = $startAfter;
            }

            while (true) {
                if (!empty($maxKeys)) {
                    // Adjust maxKeys parameter for each iteration of ListObjects
                    //
                    $params['MaxKeys'] = intval($maxKeys) - count($objects);
                }

                $listObjects = $this->s3->ListObjectsV2($params);

                if (is_array($listObjects['Contents'])) {
                    $result = true;
                    foreach ($listObjects['Contents'] as $object) {
                        $objects[] = $object;
                    }
                }

                if (!empty($listObjects['IsTruncated'])) {
                    if (!empty($maxKeys) && count($objects) >= intval($maxKeys)) {
                        $objects = array_splice($objects, 0, intval($maxKeys));
                        break;
                    }

                    $params['ContinuationToken'] = $listObjects['NextContinuationToken'];

                    continue;
                }
                break;
            }
        } catch (AwsException $e) {
            $this->logAwsException($e);
            $this->setStatusConnect(false);

            return [
                'result' => $result,
                'objects' => [],
                'error' => $e->getAwsErrorMessage(),
            ];
        } catch (Exception $e) {
            $this->lastErrorMessage = $e->getMessage();
            $this->setStatusConnect(false);
            Log::error(sprintf("ERROR in %s(): %s", __FUNCTION__, $e->getMessage()));

            return [
                'result' => $result,
                'objects' => [],
                'error' => $e->getMessage(),
            ];
        }

        return [
            'result' => $result,
            'objects' => $objects,
            'objectCount' => count($objects),
        ];
    }

    public function deleteObjects($objectKeys, $bucketName = null)
    {
        $result = false;
        $objects = [];

        if (empty($bucketName)) {
            $bucketName = $this->defaultBucketName;
        }

        try {
            // Prepare a batch of objects to delete
            $batch = [];
            foreach ($objectKeys as $objectKey) {
                $objectKey = ltrim($objectKey, '/');
                $batch[] = $this->s3->getCommand('DeleteObject', [
                    'Bucket' => $bucketName,
                    'Key' => $objectKey,
                ]);
            }
        } catch (Exception $e) {
            if ($e instanceof AwsException) {
                $this->logAwsException($e);
            } else {
                Log::error(sprintf("ERROR in %s(): %s", __FUNCTION__, $e->getMessage()));
            }

            return [
                'result' => $result,
                'error' => $e->getMessage(),
            ];
        }

        // Execute the command batch
        $objects = [];
        $exceptions = [];
        try {
            $responses = CommandPool::batch($this->s3, $batch);
            $result = true;
            foreach ($responses as $response) {
                if ($response instanceof \Aws\Result) {
                    $objects[] = $response['@metadata']['effectiveUri'];
                }
                if ($response instanceof AwsException) {
                    $exceptions[] = $response->getAwsErrorMessage();
                }
            }
        } catch (Exception $e) {
            Log::error(sprintf("ERROR in %s(): %s", __FUNCTION__, $e->getMessage()));
        }

        return [
            'result' => $result,
            'objects' => $objects,
            'objectCount' => count($objects),
            'exceptions' => $exceptions,
        ];
    }

    /*
     * Delete a single S3 key
     *
     */
    public function deleteObject($objectKey, $bucketName = null)
    {
        return $this->deleteObjects([$objectKey], $bucketName);
    }

    /*
     * Delete all objects matching a given path
     *
     */
    public function deletePathRecursive($path, $bucketName = null)
    {
        if (empty($bucketName)) {
            $bucketName = $this->defaultBucketName;
        }

        if ($path == '/') {
            return [
                'result' => false,
                'error' => "Deleting path / is not supported",
            ];
        }

        // Strip the leading slash
        $path = ltrim($path, '/');

        // Ensure a trailing slash is always present for safety
        $path = rtrim($path, '/') . '/';

        // Iterate the objects in the source path and prepare a batch of
        // CopyObject operations to copy all objects to destination path
        // The use of getIterator here gets around the limit of 1000 objects
        // per ListObjects request.
        $iterator = $this->getListObjectsByIteratorCommand($bucketName, $path);

        $objectKeys = [];
        foreach ($iterator as $object) {
            $objectKeys[] = $object['Key'];
        }

        return $this->deleteObjects($objectKeys, $bucketName);
    }

    public function listFolderFiles($folder)
    {
        // The use of getIterator here gets around the limit of 1000 objects
        // per ListObjects request.
        $objects = $this->getListObjectsByIteratorCommand($this->defaultBucketName, $folder);

        $myArray = [];
        foreach ($objects as $object) {
            $myArray[] = $object['Key'];
        }

        return $myArray;
    }

    /**
     * @return mixed
     */
    private function getListObjectsByIteratorCommand($bucketName, $path)
    {
        return $this->s3->getIterator('ListObjects', [
            "Bucket" => $bucketName,
            "Prefix" => $path, // must have the trailing forward slash "/"
        ]);
    }

    private function logAwsException(AwsException $e)
    {
        $this->lastAwsErrorMessage = $e->getAwsErrorMessage();
        $this->lastErrorMessage = $e->getMessage();
        $this->lastAwsException = $e;

        Log::error(
            sprintf(
                "AWS Exception: Type: %s Code: %s Message: %s",
                $e->getAwsErrorType(),
                $e->getAwsErrorCode(),
                $e->getAwsErrorMessage()
            )
        );
    }

    private function setStatusConnect($status)
    {
        $this->statusConnect = $status;
    }

    public function getStatusConnect()
    {
        return $this->statusConnect;
    }
}
