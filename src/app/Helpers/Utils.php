<?php

namespace App\Helpers;

use App\Exceptions\BadRequestException;
use App\Models\Order;
use GuzzleHttp\Psr7\Query;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use libphonenumber\PhoneNumberFormat;
use libphonenumber\PhoneNumberType;

class Utils
{
    /**
     * Determine the server 'upload_max_filesize' as bytes.
     * Get the max filesize base on server config or tenant config
     *
     * @return int
     */
    public static function getUploadMaxFileSize()
    {
        $uploadMaxFileSize = self::getSystemUploadMaxFileSize();
        $maxFileSize = config('global.storage.max_file_size') ?? $uploadMaxFileSize;

        if ($uploadMaxFileSize < $maxFileSize) {
            Log::warning('The upload max filesize of PHP config is less than the configured max file size');
            $maxFileSize = $uploadMaxFileSize;
        }

        return $maxFileSize;
    }

    public static function getSystemUploadMaxFileSize()
    {
        if (is_numeric($uploadMaxFileSize = ini_get('upload_max_filesize'))) {
            return $uploadMaxFileSize;
        }

        $metric = strtoupper(substr($uploadMaxFileSize, -1));
        $uploadMaxFileSize = (int)$uploadMaxFileSize;

        switch ($metric) {
            case 'K':
                return $uploadMaxFileSize * 1024;
            case 'M':
                return $uploadMaxFileSize * 1048576;
            case 'G':
                return $uploadMaxFileSize * 1073741824;
            default:
                return $uploadMaxFileSize;
        }
    }

    /**
     * Generate a unique identifier based on the current timestamp.
     *
     * Schema: {prefix}{unixEpochMicrotime}{channelIndicator}{referenceSuffix}
     *
     * Example: PRE16377357104511MP
     *
     * - PRE = Environment prefix (configurable) - optional and not recommended
     *         for PROD
     * - 16377357104511 = Microtime(true)
     * - M = First letter of channel type in uppercase (M for mobile in this
     *        example)
     * - P = Reference suffix - indicates the type of reference, currently we
     *       use the following:
     *       - O = Order
     *       - P = Order Payment
     *
     * @param  string  $suffix  The reference suffix.
     * @param  string  $channelType  The channel type this reference is being
     *                               generated for.
     * @param  ?string  $prefix  Reference prefix (optional in all
     *                           environments except for local & dev)
     * @param  ?int  $maxLength  Optional maximum length.
     */
    public static function generateUniqueIdentifier(string $suffix, string $channelType, ?string $prefix, ?int $maxLength = null): string
    {
        if ($prefix === null && App::environment(['local', 'dev'])) {
            throw new BadRequestException('reference_prefix_not_populated', 500);
        }

        $suffix = sprintf(
            '%s%s%s',
            microtime(true) * 10000,
            strtoupper(substr($channelType, 0, 1)),
            $suffix
        );

        if ($maxLength !== null) {
            $len = $maxLength - strlen($suffix);
            $prefix = substr($prefix, 0, $len);
        }

        return sprintf(
            '%s%s',
            strtoupper($prefix),
            $suffix,
        );
    }

    public static function getUploadFailedMessage(): string
    {
        $uploadMaxFileSize = self::getUploadMaxFileSize() / (1024 * 1024);

        return "The file failed to upload. A maximum filesize of {$uploadMaxFileSize}M is supported";
    }

    /**
     * Partially mask a string with stars
     * eg extrodinary -> ext*******y
     */
    public static function maskString(string $str, $maxFirst = 3, $maxLast = 1): string
    {
        // Adapt the maxLast value depending on the length of the incoming string
        $len = strlen($str);
        if ($len <= $maxFirst + $maxLast) {
            $maxLast = 0;
        }

        $regex = sprintf('/^(.{1,%d})(.*)(.{%d})$/', $maxFirst, $maxLast);
        if (preg_match($regex, $str, $matches)) {
            $str = $matches[1] . str_repeat('*', strlen($matches[2])) . $matches[3];
        }

        return $str;
    }

    /**
     * Partially mask an email address with stars
     *
     * <EMAIL> -> use*****@out***k.com
     */
    public static function maskEmailAddress(string $email): ?string
    {
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return null;
        }

        $emailParts = explode('@', $email);
        $domainParts = explode('.', $emailParts[1]);

        $userPart = array_shift($emailParts);
        $firstDomain = array_shift($domainParts);

        return sprintf(
            "%s@%s.%s",
            Utils::maskString($userPart, 3, 1),
            Utils::maskString($firstDomain, 3, 2),
            implode('.', $domainParts)
        );
    }

    public static function collectToString(Collection $collections, string $field, string $separator = ', '): string
    {
        return implode(
            $separator,
            $collections->pluck($field)
                ->unique()->toArray()
        );
    }

    public static function generateSignature(
        string $path,
        string $method,
        ?array $params = []
    ): string {
        $secret = config('app.hash_hmac_key');
        $query = Query::build($params);
        $payload = "$method {$path}" . ($query ? "?{$query}" : '');

        $hash = hash_hmac('sha256', $payload, $secret);

        Log::debug('generateSignature', [
            'payload' => $payload,
            'hash' => $hash,
        ]);

        return $hash;
    }

    public static function mapSpecialQuote(string $str): string
    {
        $chrMap = [
            // Windows codepage 1252
            "\xC2\x82" => "'", // U+0082⇒U+201A single low-9 quotation mark
            "\xC2\x84" => '"', // U+0084⇒U+201E double low-9 quotation mark
            "\xC2\x8B" => "'", // U+008B⇒U+2039 single left-pointing angle quotation mark
            "\xC2\x91" => "'", // U+0091⇒U+2018 left single quotation mark
            "\xC2\x92" => "'", // U+0092⇒U+2019 right single quotation mark
            "\xC2\x93" => '"', // U+0093⇒U+201C left double quotation mark
            "\xC2\x94" => '"', // U+0094⇒U+201D right double quotation mark
            "\xC2\x9B" => "'", // U+009B⇒U+203A single right-pointing angle quotation mark

            // Regular Unicode     // U+0022 quotation mark (")
            // U+0027 apostrophe     (')

            "\xC2\xAB" => '"', // U+00AB left-pointing double angle quotation mark
            "\xC2\xBB" => '"', // U+00BB right-pointing double angle quotation mark
            "\xE2\x80\x98" => "'", // U+2018 left single quotation mark
            "\xE2\x80\x99" => "'", // U+2019 right single quotation mark
            "\xE2\x80\x9A" => "'", // U+201A single low-9 quotation mark
            "\xE2\x80\x9B" => "'", // U+201B single high-reversed-9 quotation mark
            "\xE2\x80\x9C" => '"', // U+201C left double quotation mark
            "\xE2\x80\x9D" => '"', // U+201D right double quotation mark
            "\xE2\x80\x9E" => '"', // U+201E double low-9 quotation mark
            "\xE2\x80\x9F" => '"', // U+201F double high-reversed-9 quotation mark
            "\xE2\x80\xB9" => "'", // U+2039 single left-pointing angle quotation mark
            "\xE2\x80\xBA" => "'", // U+203A single right-pointing angle quotation mark
        ];
        $chr = array_keys($chrMap); // but: for efficiency you should
        $rpl = array_values($chrMap); // pre-calculate these two arrays

        return str_replace($chr, $rpl, html_entity_decode($str, ENT_QUOTES, 'UTF-8'));
    }

    public static function formatPhoneNumber(?PhoneNumberType $phone, $format = PhoneNumberFormat::E164, $stripWhitespace = false): array|string|null
    {
        if (!$phone) {
            return null;
        }

        $formattedPhone = app('PhoneNumberUtil')->format($phone, $format);
        if ($stripWhitespace) {
            return str_replace(' ', '', $formattedPhone);
        }

        return $formattedPhone;
    }

    /**
     * Normalize French phone number to E164 format (+33)
     */
    public static function normalizeFrenchPhoneNumber(string|int|null $phoneNumber): ?string
    {
        if ($phoneNumber === null || $phoneNumber === '') {
            return null;
        }
        $phoneNumber = trim((string) $phoneNumber);

        // Remove all non-digit characters except +
        $cleaned = preg_replace('/[^\d+]/', '', $phoneNumber);

        // If already starts with +33, return as is
        if (str_starts_with($cleaned, '+33')) {
            return $cleaned;
        }

        // If starts with 33 (without +), add +
        if (str_starts_with($cleaned, '33')) {
            return '+' . $cleaned;
        }

        // If starts with 0, replace with +33
        if (str_starts_with($cleaned, '0')) {
            return '+33' . substr($cleaned, 1);
        }

        // If it's a 9-10 digit number (French mobile), add +33
        if (preg_match('/^(\d{9,10})$/', $cleaned)) {
            return '+33' . $cleaned;
        }

        // If it's already in international format but not +33, try to parse and convert
        if (str_starts_with($cleaned, '+')) {
            try {
                $phoneUtil = app('PhoneNumberUtil');
                $parsedNumber = $phoneUtil->parse($cleaned);

                // Check if it's a valid French number
                if ($phoneUtil->getRegionCodeForNumber($parsedNumber) === 'FR') {
                    return $phoneUtil->format($parsedNumber, PhoneNumberFormat::E164);
                }
            } catch (\Exception $e) {
                // If parsing fails, return null
                return null;
            }
        }

        return null;
    }
}
