<?php

namespace App\Helpers;

use Exception;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class FileHelper
{
    protected S3Helper $s3Helper;

    /**
     * FileHelper constructor.
     *
     * @throws Exception
     */
    public function __construct()
    {
        $this->initialS3Helper();
    }

    /**
     * Initial AWS S3 helper
     *
     * @throws Exception
     */
    private function initialS3Helper(): void
    {
        // Get S3 tenant configuration
        $s3Config = config('filesystems.disks.s3');

        // Initial S3 helper
        $s3Helper = new S3Helper($s3Config);

        // Check AWS S3 connection is successful
        if (!$s3Helper->getStatusConnect()) {
            throw new Exception('Unable to connect to AWS S3 POS Bucket: ' . $s3Helper->lastAwsErrorMessage);
        }

        $this->s3Helper = $s3Helper;
    }

    /**
     * Upload file from client to server local
     */
    public function uploadFileToLocal($fileUpload, string $model, string $type): false|string
    {
        // Get file info
        $fileName = $fileUpload->getClientOriginalName();

        $uploadPathTemp = "temp/{$model}/{$type}";

        // Put temp file to local and return temp path
        return Storage::disk('public')->putFileAs($uploadPathTemp, $fileUpload, $fileName);
    }

    /**
     * Put temp file to S3
     *
     * @param  null  $mimeType
     *
     * @throws FileNotFoundException
     * @throws Exception
     */
    public function putTempFileToS3($tempPath, $mimeType): array
    {
        // Check temp path exactly
        if (preg_match('/^temp\/(.*)/', $tempPath, $matches)) {
            $uploadPath = $matches[1];
        } else {
            throw new Exception('Temp path invalid format');
        }

        // Get content of temp file
        $content = Storage::disk('public')->get($tempPath);

        // Put temp file to s3 by helper
        $result = $this->s3Helper->putObject($uploadPath, $content, [], $mimeType);

        // Delete temp file when upload success
        Storage::disk('public')->delete($tempPath);

        return [
            'path' => $result['ObjectURL'],
            'etag' => str_replace('"', '', $result['ETag']),
        ];
    }

    /**
     * Delete S3 Object by live path
     *
     * @param  $path  //Live path of object
     * @return bool True if deleted, false if failed
     */
    public function deleteS3FileByLivePath($path): bool
    {
        // Get s3 config bucket name and initial prefix path
        $s3Bucket = config('filesystems.disks.s3.bucket');
        $s3Region = config('filesystems.disks.s3.region');
        $AWSS3PrefixPath = 'https://' . $s3Bucket . '.s3.' . $s3Region . '.amazonaws.com';

        // Generate pattern match live path
        $patternMatch = str_replace('/', '\/', $AWSS3PrefixPath);

        // Check live path exactly
        if (preg_match("/^$patternMatch(.*)/", $path, $matches)) {
            $deleteKey = $matches[1];
        } else {
            Log::error('Delete object by live path failed: Live path to delete is invalid');

            return false;
        }

        try {
            $this->s3Helper->deleteObject($deleteKey);

            return true;
        } catch (\Exception $e) {
            Log::error('Delete object from S3 failed: ' . $e->getMessage(), ['key' => $deleteKey]);

            return false;
        }
    }

    /**
     * @param  null  $mimeType
     *
     * @throws Exception
     */
    public function initialFileTempData(
        $fileUpload,
        string $model,
        string $type,
        $mimeType = null
    ): array {
        // Put temp file to local
        $tempPath = $this->uploadFileToLocal($fileUpload, $model, $type);

        // If no mime type is specified, get the mime type of the temp file.
        $mimeType = $mimeType ?? mime_content_type(Storage::disk('public')->path($tempPath));
        if (!$mimeType) {
            Storage::disk('public')->delete($tempPath);
            throw new Exception("Unable to determine the mime type of file");
        }

        // Initial file data to save to database
        $fileSize = $fileUpload->getSize();
        $hash = hash_file('sha256', $fileUpload);

        return [
            'type' => $type,
            'path' => $tempPath,
            'size' => $fileSize,
            'sha256_hash' => $hash,
            'mime_type' => $mimeType,
        ];
    }
}
