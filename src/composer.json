{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.3", "aws/aws-sdk-php": "^3.351", "dedoc/scramble": "^0.12.22", "giggsey/libphonenumber-for-php": "^9.0", "laravel/framework": "^12.0", "laravel/sanctum": "^4.0", "laravel/telescope": "^5.9", "laravel/tinker": "^2.9", "spatie/laravel-permission": "^6.20"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.2", "enlightn/security-checker": "^2.0", "fakerphp/faker": "^1.23", "larastan/larastan": "^3.0", "laravel/pail": "^1.1", "laravel/pint": "^1.18", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "php-parallel-lint/php-parallel-lint": "^1.4", "phpmd/phpmd": "^2.15", "phpro/grumphp": "^2.9", "phpunit/phpunit": "^11.0.1", "squizlabs/php_codesniffer": "^3.11"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "Illuminate\\Foundation\\ComposerScripts::postUpdate", "@php artisan ide-helper:generate", "@php artisan ide-helper:meta"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"], "phpcs": "./vendor/bin/phpcs --standard=PSR2 ./", "phpstan": "./vendor/bin/phpstan analyse ./ --no-progress"}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true, "phpro/grumphp": true}}, "minimum-stability": "stable", "prefer-stable": true}