/**
 * DataTables Responsive Helper
 * Provides consistent responsive configuration for all DataTables in the admin panel
 */

class DataTablesResponsiveHelper {
    constructor() {
        this.defaultConfig = {
            "paging": true,
            "lengthChange": false,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "responsive": true, // Use default responsive configuration
            "language": {
                "search": "Search:",
                "lengthMenu": "Show _MENU_ entries",
                "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                "infoEmpty": "Showing 0 to 0 of 0 entries",
                "infoFiltered": "(filtered from _MAX_ total entries)",
                "paginate": {
                    "first": "First",
                    "last": "Last",
                    "next": "Next",
                    "previous": "Previous"
                }
            }
        };
    }

    /**
     * Get responsive configuration with modal display
     */
    getResponsiveConfig() {
        try {
            // Check if responsive extension is loaded
            if (jQuery.fn.dataTable && jQuery.fn.dataTable.Responsive) {
                return {
                    details: {
                        display: jQuery.fn.dataTable.Responsive.display.modal({
                            header: function (row) {
                                var data = row.data();
                                return 'Details for ' + (data[1] || data[0] || 'Item');
                            }
                        }),
                        renderer: jQuery.fn.dataTable.Responsive.renderer.tableAll()
                    }
                };
            }
        } catch (error) {
            console.warn('DataTables Responsive extension not fully loaded, using fallback:', error);
        }
        
        // Fallback to default responsive
        return true;
    }

    /**
     * Initialize DataTable with responsive configuration
     * @param {string} tableId - The table ID
     * @param {object} customConfig - Custom configuration to merge with default
     */
    initTable(tableId, customConfig = {}) {
        if (typeof jQuery === 'undefined') {
            console.error('jQuery is not loaded');
            return null;
        }
        
        const config = jQuery.extend(true, {}, this.defaultConfig, customConfig);
        
        // Set responsive configuration
        config.responsive = this.getResponsiveConfig();

        return jQuery(tableId).DataTable(config);
    }

    /**
     * Initialize multiple tables with the same configuration
     * @param {Array} tableIds - Array of table IDs
     * @param {object} customConfig - Custom configuration to merge with default
     */
    initTables(tableIds, customConfig = {}) {
        const tables = [];
        tableIds.forEach(tableId => {
            tables.push(this.initTable(tableId, customConfig));
        });
        return tables;
    }

    /**
     * Get configuration for tables with action columns
     * @param {Array} actionColumnIndexes - Array of action column indexes
     * @param {Array} centerColumnIndexes - Array of columns to center (optional, all columns will be centered by default)
     * @returns {object} Configuration object
     */
    getActionTableConfig(actionColumnIndexes = [], centerColumnIndexes = []) {
        const columnDefs = [];

        // Get total number of columns (assuming action columns are the last ones)
        const totalColumns = actionColumnIndexes.length > 0 ? 
            Math.max(...actionColumnIndexes) + 1 : 
            (centerColumnIndexes.length > 0 ? Math.max(...centerColumnIndexes) + 1 : 0);

        // Center all columns by default
        const allColumnIndexes = Array.from({length: totalColumns}, (_, i) => i);
        
        // If specific center columns are provided, use them; otherwise center all
        const columnsToCenter = centerColumnIndexes.length > 0 ? centerColumnIndexes : allColumnIndexes;
        
        // Add center alignment for columns
        columnDefs.push({
            "targets": columnsToCenter,
            "className": "text-center"
        });

        // Add action column configuration
        actionColumnIndexes.forEach(index => {
            columnDefs.push({
                "targets": index,
                "orderable": false,
                "searchable": false
            });
        });

        return {
            "columnDefs": columnDefs
        };
    }

    /**
     * Get configuration for tables with checkbox columns
     * @param {number} checkboxColumnIndex - Index of checkbox column
     * @param {Array} actionColumnIndexes - Array of action column indexes
     * @param {Array} centerColumnIndexes - Array of columns to center (optional, all columns will be centered by default)
     * @returns {object} Configuration object
     */
    getCheckboxTableConfig(checkboxColumnIndex = 0, actionColumnIndexes = [], centerColumnIndexes = []) {
        const columnDefs = [];

        // Add checkbox column configuration
        columnDefs.push({
            "targets": checkboxColumnIndex,
            "orderable": false,
            "searchable": false,
            "className": "text-center"
        });

        // Get total number of columns
        const allIndexes = [checkboxColumnIndex, ...actionColumnIndexes, ...centerColumnIndexes];
        const totalColumns = allIndexes.length > 0 ? Math.max(...allIndexes) + 1 : 0;

        // Center all columns by default
        const allColumnIndexes = Array.from({length: totalColumns}, (_, i) => i);
        
        // If specific center columns are provided, use them; otherwise center all (except checkbox)
        const columnsToCenter = centerColumnIndexes.length > 0 ? 
            centerColumnIndexes : 
            allColumnIndexes.filter(i => i !== checkboxColumnIndex);
        
        // Add center alignment for columns
        if (columnsToCenter.length > 0) {
            columnDefs.push({
                "targets": columnsToCenter,
                "className": "text-center"
            });
        }

        // Add action column configuration
        actionColumnIndexes.forEach(index => {
            columnDefs.push({
                "targets": index,
                "orderable": false,
                "searchable": false
            });
        });

        return {
            "columnDefs": columnDefs
        };
    }

    /**
     * Refresh responsive display for a table
     * @param {string} tableId - The table ID
     */
    refreshResponsive(tableId) {
        if (typeof jQuery === 'undefined') {
            console.error('jQuery is not loaded');
            return;
        }
        
        const table = jQuery(tableId).DataTable();
        if (table) {
            table.responsive.recalc();
        }
    }

    /**
     * Refresh responsive display for multiple tables
     * @param {Array} tableIds - Array of table IDs
     */
    refreshResponsiveMultiple(tableIds) {
        tableIds.forEach(tableId => {
            this.refreshResponsive(tableId);
        });
    }

    /**
     * Handle window resize for responsive tables
     */
    handleResize() {
        if (typeof jQuery === 'undefined') {
            console.error('jQuery is not loaded');
            return;
        }
        
        jQuery(window).on('resize', () => {
            jQuery('.dataTable').each(function() {
                const table = jQuery(this).DataTable();
                if (table && table.responsive) {
                    table.responsive.recalc();
                }
            });
        });
    }

    /**
     * Check if DataTables Responsive is ready
     */
    isResponsiveReady() {
        return !!(window.jQuery && jQuery.fn.dataTable && jQuery.fn.dataTable.Responsive);
    }

    /**
     * Initialize responsive handling
     */
    init() {
        this.handleResize();
        
        // Add custom responsive breakpoints - check if responsive extension is loaded
        if (this.isResponsiveReady() && jQuery.fn.dataTable.ext && jQuery.fn.dataTable.ext.responsive) {
            try {
                jQuery.fn.dataTable.ext.responsive.breakpoints = [
                    { name: 'desktop', width: Infinity },
                    { name: 'tablet-l', width: 1024 },
                    { name: 'tablet-p', width: 768 },
                    { name: 'mobile-l', width: 480 },
                    { name: 'mobile-p', width: 320 }
                ];
            } catch (error) {
                console.warn('Could not set custom breakpoints:', error);
            }
        }
    }
}

// Create global instance
window.DataTablesResponsive = new DataTablesResponsiveHelper();

// Auto-initialize when document is ready and DataTables Responsive is loaded
function waitForJQueryAndInit() {
    if (typeof jQuery !== 'undefined') {
        jQuery(document).ready(function() {
            // Wait for DataTables Responsive to be loaded
            if (window.DataTablesResponsive.isResponsiveReady()) {
                window.DataTablesResponsive.init();
            } else {
                // Retry after a short delay
                setTimeout(function() {
                    if (window.DataTablesResponsive.isResponsiveReady()) {
                        window.DataTablesResponsive.init();
                    } else {
                        console.warn('DataTables Responsive not available after retry');
                    }
                }, 100);
            }
        });
    } else {
        setTimeout(waitForJQueryAndInit, 50);
    }
}

waitForJQueryAndInit();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DataTablesResponsiveHelper;
} 