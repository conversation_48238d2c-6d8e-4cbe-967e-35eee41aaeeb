@extends('layouts.auth')

@section('title', 'Reset Password')
@section('page-title', 'Reset Password')

@section('content')
    <div class="login-box">
        <div class="card card-outline card-primary mt-4">
            <div class="card-header text-center">
                <h3 class="card-title">Reset Password</h3>
            </div>
            <div class="card-body">
                @if(session('error'))
                    <div class="alert alert-danger">{{ session('error') }}</div>
                @endif
                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif
                    <form method="POST" action="{{ route('forgot-password.reset-password') }}">
                    @csrf
                    <div class="form-group">
                        <label for="password" class="form-label">New Password</label>
                        <input type="password" class="form-control @error('password') is-invalid @enderror" id="password" name="password" required>
                        @error('password')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                        <div class="form-group mt-3">
                            <label for="password_confirmation" class="form-label">Confirm New Password</label>
                            <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required>
                        </div>
                    <div class="form-group mt-3">
                        <button type="submit" class="btn btn-primary">Reset Password</button>
                    </div>
                </form>
                <div class="mt-3 text-center">
                    <a href="{{ route('login') }}">Back to Login</a>
                </div>
            </div>
        </div>
    </div>
@endsection
