@extends('layouts.auth')

@section('title', 'Forgot Password')
@section('page-title', 'Forgot Password')

@section('content')
    <div class="login-box">
        <div class="card card-outline card-primary mt-4">
            <div class="card-header text-center">
                <h3 class="card-title">Forgot Password</h3>
            </div>
            <div class="card-body">
                @if(session('error'))
                    <div class="alert alert-danger">{{ session('error') }}</div>
                @endif
                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif
                <form method="POST" action="{{ route('forgot-password.send-otp') }}">
                    @csrf
                    <div class="form-group">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="text" class="form-control @error('phone') is-invalid @enderror" id="phone" name="phone" value="{{ old('phone') }}" required autofocus>
                        @error('phone')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group mb-0">
                        <button type="submit" class="btn btn-primary" id="send-otp-btn">Send OTP</button>
                    </div>
                </form>
                <div class="mt-3 text-center">
                    <a href="{{ route('login') }}">Back to Login</a>
                </div>
            </div>
        </div>
    </div>
@endsection
