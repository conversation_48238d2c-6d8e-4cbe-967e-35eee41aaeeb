@extends('layouts.auth')

@section('title', 'Enter OTP - Forgot Password')
@section('page-title', 'Enter OTP - Forgot Password')

@section('content')
    <div class="login-box">
        <div class="card card-outline card-primary mt-4">
            <div class="card-header text-center">
                <h3 class="card-title">Enter OTP</h3>
            </div>
            <div class="card-body">
                @if(session('error'))
                    <div class="alert alert-danger">{{ session('error') }}</div>
                @endif
                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif

                @if(isset($otp) && $otp)
                    <div class="alert alert-info">
                        <strong>Development Mode:</strong> Your OTP is: <code class="text-white text-bold">{{ $otp }}</code>
                    </div>
                @endif

                <form method="POST" action="{{ route('forgot-password.verify-otp') }}">
                    @csrf
                    <div class="form-group">
                        <label for="otp" class="form-label">OTP Code</label>
                        <input type="text" class="form-control @error('otp') is-invalid @enderror" id="otp" name="otp" maxlength="6" required autofocus>
                        @error('otp')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group mb-0">
                        <button type="submit" class="btn btn-primary">Verify OTP</button>
                    </div>
                </form>
                <form method="POST" action="{{ route('forgot-password.send-otp') }}" class="mt-3">
                    @csrf
                    <input type="hidden" name="resend" value="1">
                    <input type="hidden" name="phone" value="{{ $phone }}">
                    <button type="submit" class="btn btn-link">Didn't receive the OTP? Resend</button>
                </form>
                <div class="mt-3">
                    <a href="{{ route('login') }}" class="btn btn-link">Back to Login</a>
                </div>
            </div>
        </div>
    </div>
@endsection
