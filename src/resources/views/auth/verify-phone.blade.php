@extends('layouts.auth')

@section('title', 'Verify phone number')
@section('page-title', 'Verify phone number')

@section('content')
    <div class="login-box">
        <div class="card card-outline card-primary mt-4">
            <div class="card-header text-center">
                <h3 class="card-title">Verify phone number</h3>
            </div>
            <div class="card-body">
                @if(!$errors->any())
                    <p class="text-center">
                        OTP code has been sent to phone number <strong>***{{ substr($phone, -3) }}</strong>.
                    </p>
                @endif

                @if(isset($otp) && $otp)
                    <div class="alert alert-info">
                        <strong>Development Mode:</strong> Your OTP is: <code class="text-white text-bold">{{ $otp }}</code>
                    </div>
                @endif

                @if ($errors->any())
                    <div class="alert alert-danger">
                        @if ($errors->count() === 1)
                            <p class="mb-0">{{ $errors->first() }}</p>
                        @else
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        @endif
                    </div>
                @endif
                @if (session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif
                <form action="{{ route('verify-phone.submit') }}" method="POST">
                    @csrf
                    <div class="form-group">
                        <label for="otp">OTP Code</label>
                        <input type="text" name="otp" id="otp" class="form-control" maxlength="6" required autofocus placeholder="Enter OTP Code">
                    </div>
                    <div class="form-group mb-0">
                        <button type="submit" class="btn btn-primary btn-block">Verify</button>
                    </div>
                </form>
                <form action="{{ route('verify-phone.send-otp') }}" method="POST" class="mt-3">
                    @csrf
                    <button type="submit" class="btn btn-link btn-block">Resend OTP</button>
                </form>
            </div>
        </div>
    </div>
@endsection
