<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Admin Login | Ifingo</title>

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    @vite(['resources/css/adminlte.css'])
</head>
<body class="hold-transition login-page">
    <div class="login-box">
        <div class="card card-outline card-primary">
            <div class="card-header text-center">
                <a href="/" class="h1">Ifingo</a>
            </div>
            <div class="card-body">
                @if ($errors->any())
                    <div class="alert alert-danger alert-dismissible">
                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                        @if ($errors->count() === 1)
                            <p class="mb-0">{{ $errors->first() }}</p>
                        @else
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        @endif
                    </div>
                @endif

                @if (session('success'))
                    <div class="alert alert-success alert-dismissible">
                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                        <i class="fas fa-check-circle mr-2"></i>
                        {{ session('success') }}
                    </div>
                @endif

                @if (session('error'))
                    <div class="alert alert-danger alert-dismissible">
                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        {{ session('error') }}
                    </div>
                @endif

                @if (session('warning'))
                    <div class="alert alert-warning alert-dismissible">
                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        {{ session('warning') }}
                    </div>
                @endif

                <form action="{{ route('login.submit') }}" method="post" id="loginForm">
                    @csrf
                    <div class="input-group mb-3">
                        <input type="email" name="email" id="email" class="form-control"
                               placeholder="Email" value="{{ old('email') }}" required autofocus>
                        <div class="input-group-append">
                            <div class="input-group-text">
                                <span class="fas fa-envelope"></span>
                            </div>
                        </div>
                    </div>
                    <div class="input-group mb-3">
                        <input type="password" name="password" id="password" class="form-control"
                               placeholder="Password" required>
                        <div class="input-group-append">
                            <div class="input-group-text">
                                <span class="fas fa-lock"></span>
                            </div>
                        </div>
                    </div>
                    <div class="row align-items-center">
                        <div class="col-8">
                            <div class="icheck-primary">
                                <input type="checkbox" id="remember" name="remember" value="1" {{ old('remember') ? 'checked' : '' }}>
                                <label for="remember" class="mb-0 ml-2">
                                    Remember Me
                                </label>
                            </div>
                        </div>
                        <div class="col-4">
                            <button type="submit" id="loginBtn" class="btn btn-primary btn-block" disabled>
                                Sign In
                            </button>
                        </div>
                    </div>
                </form>

                <div class="mt-3 text-center">
                    <a href="{{ route('forgot-password.request') }}">Forgot Password?</a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    @vite(['resources/js/adminlte.js'])

    <script>
        $(document).ready(function() {
            // Clear any sensitive data from previous sessions
            clearSensitiveData();

            const emailInput = $('#email');
            const passwordInput = $('#password');
            const loginBtn = $('#loginBtn');

            function validateForm() {
                const email = emailInput.val().trim();
                const password = passwordInput.val().trim();

                if (email && password) {
                    loginBtn.prop('disabled', false);
                } else {
                    loginBtn.prop('disabled', true);
                }
            }

            emailInput.on('input', validateForm);
            passwordInput.on('input', validateForm);

            validateForm();

            $('#loginForm').on('submit', function() {
                loginBtn.prop('disabled', true).text('Signing In...');
            });

            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);

            function clearSensitiveData() {
                try {
                    // Clear any sensitive data that might be left from previous sessions
                    sessionStorage.clear();

                    // Clear specific sensitive items from localStorage
                    const sensitiveKeys = ['auth_token', 'user_data', 'admin_session', 'temp_data'];
                    sensitiveKeys.forEach(key => {
                        localStorage.removeItem(key);
                    });

                    console.log('Sensitive data cleared on login page load');
                } catch (error) {
                    console.warn('Failed to clear some sensitive data:', error);
                }
            }
        });
    </script>
</body>
</html>
