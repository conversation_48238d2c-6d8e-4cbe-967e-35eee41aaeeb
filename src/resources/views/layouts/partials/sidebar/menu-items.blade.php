@if(auth()->user() && auth()->user()->hasRole('admin'))
    <li class="nav-item">
        <a href="{{ route('admin.dashboard') }}" class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
            <i class="nav-icon fas fa-tachometer-alt"></i>
            <p>Dashboard</p>
        </a>
    </li>

    {{-- Users Menu --}}
    <li class="nav-item has-treeview {{ request()->routeIs('admin.users.*') ? 'menu-is-opening menu-open' : '' }}">
        <a href="{{ route('admin.users.index') }}" class="nav-link {{ request()->routeIs('admin.users.*') ? 'active' : '' }}">
            <i class="nav-icon fas fa-users"></i>
            <p>
                Users
                <i class="right fas fa-angle-left"></i>
            </p>
        </a>
        <ul class="nav nav-treeview">
            <li class="nav-item">
                <a href="{{ route('admin.users.index') }}" class="nav-link {{ request()->routeIs('admin.users.index') ? 'active' : '' }}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Users List</p>
                </a>
            </li>
            <li class="nav-item">
                <a href="{{ route('admin.users.create') }}" class="nav-link {{ request()->routeIs('admin.users.create') ? 'active' : '' }}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Add User</p>
                </a>
            </li>
        </ul>
    </li>

    {{-- Products Menu --}}
    <li class="nav-item has-treeview {{ request()->routeIs('admin.products.*') ? 'menu-is-opening menu-open' : '' }}">
        <a href="{{ route('admin.products.index') }}" class="nav-link {{ request()->routeIs('admin.products.*') ? 'active' : '' }}">
            <i class="nav-icon fas fa-box"></i>
            <p>
                Products
                <i class="right fas fa-angle-left"></i>
            </p>
        </a>
        <ul class="nav nav-treeview">
            <li class="nav-item">
                <a href="{{ route('admin.products.index') }}" class="nav-link {{ request()->routeIs('admin.products.index') ? 'active' : '' }}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Product List</p>
                </a>
            </li>
            <li class="nav-item">
                <a href="{{ route('admin.products.import-export') }}" class="nav-link {{ request()->routeIs('admin.products.import-export') ? 'active' : '' }}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Import/Export</p>
                </a>
            </li>
        </ul>
    </li>

    {{-- Rewards Menu --}}
    <li class="nav-item has-treeview {{ request()->routeIs('admin.rewards.*') ? 'menu-is-opening menu-open' : '' }}">
        <a href="{{ route('admin.rewards.index') }}" class="nav-link {{ request()->routeIs('admin.rewards.*') ? 'active' : '' }}">
            <i class="nav-icon fas fa-gift"></i>
            <p>
                Rewards
                <i class="right fas fa-angle-left"></i>
            </p>
        </a>
        <ul class="nav nav-treeview">
            <li class="nav-item">
                <a href="{{ route('admin.rewards.index') }}" class="nav-link {{ request()->routeIs('admin.rewards.index') ? 'active' : '' }}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Rewards List</p>
                </a>
            </li>
            <li class="nav-item">
                <a href="{{ route('admin.rewards.create') }}" class="nav-link {{ request()->routeIs('admin.rewards.create') ? 'active' : '' }}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Add New Reward</p>
                </a>
            </li>
        </ul>
    </li>

    {{-- Promotions Menu --}}
    <li class="nav-item has-treeview {{ request()->routeIs('admin.promotions.*') ? 'menu-is-opening menu-open' : '' }}">
        <a href="{{ route('admin.promotions.index') }}" class="nav-link {{ request()->routeIs('admin.promotions.*') ? 'active' : '' }}">
            <i class="nav-icon fas fa-bullhorn"></i>
            <p>
                Promotions
                <i class="right fas fa-angle-left"></i>
            </p>
        </a>
        <ul class="nav nav-treeview">
            <li class="nav-item">
                <a href="{{ route('admin.promotions.index') }}" class="nav-link {{ request()->routeIs('admin.promotions.index') ? 'active' : '' }}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Promotions List</p>
                </a>
            </li>
            <li class="nav-item">
                <a href="{{ route('admin.promotions.create') }}" class="nav-link {{ request()->routeIs('admin.promotions.create') ? 'active' : '' }}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Add New Promotion</p>
                </a>
            </li>
        </ul>
    </li>

    {{-- Vouchers Menu --}}
    <li class="nav-item has-treeview {{ request()->routeIs('admin.vouchers.*') ? 'menu-is-opening menu-open' : '' }}">
        <a href="{{ route('admin.vouchers.index') }}" class="nav-link {{ request()->routeIs('admin.vouchers.*') ? 'active' : '' }}">
            <i class="nav-icon fas fa-ticket-alt"></i>
            <p>
                Vouchers
                <i class="right fas fa-angle-left"></i>
            </p>
        </a>
        <ul class="nav nav-treeview">
            <li class="nav-item">
                <a href="{{ route('admin.vouchers.index') }}" class="nav-link {{ request()->routeIs('admin.vouchers.index') ? 'active' : '' }}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Vouchers List</p>
                </a>
            </li>
            <li class="nav-item">
                <a href="{{ route('admin.vouchers.create') }}" class="nav-link {{ request()->routeIs('admin.vouchers.create') ? 'active' : '' }}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Add New Voucher</p>
                </a>
            </li>
        </ul>
    </li>

    {{-- Retailers Menu --}}
    <li class="nav-item has-treeview {{ request()->routeIs('admin.retailers.*') ? 'menu-is-opening menu-open' : '' }}">
        <a href="{{ route('admin.retailers.index') }}" class="nav-link {{ request()->routeIs('admin.retailers.*') ? 'active' : '' }}">
            <i class="nav-icon fas fa-store"></i>
            <p>
                Retailers
                <i class="right fas fa-angle-left"></i>
            </p>
        </a>
        <ul class="nav nav-treeview">
            <li class="nav-item">
                <a href="{{ route('admin.retailers.index') }}" class="nav-link {{ request()->routeIs('admin.retailers.index') ? 'active' : '' }}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Retailers List</p>
                </a>
            </li>
            <li class="nav-item">
                <a href="{{ route('admin.retailers.create') }}" class="nav-link {{ request()->routeIs('admin.retailers.create') ? 'active' : '' }}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Add New Retailer</p>
                </a>
            </li>
        </ul>
    </li>
@endif

@if(auth()->user() && auth()->user()->hasRole('retailer'))
    <li class="nav-item">
        <a href="{{ route('retailer.dashboard') }}" class="nav-link {{ request()->routeIs('retailer.dashboard') ? 'active' : '' }}">
            <i class="nav-icon fas fa-tachometer-alt"></i>
            <p>Retailer Dashboard</p>
        </a>
    </li>
@endif
