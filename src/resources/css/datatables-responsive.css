/* Custom DataTables Responsive Styles */

/* Responsive table container */
.dataTables_wrapper {
    margin-top: 1rem;
}

/* Responsive modal styling */
.dtr-modal {
    z-index: 1050 !important;
}

.dtr-modal .modal-header {
    background-color: #007bff;
    color: white;
    border-bottom: 1px solid #0056b3;
}

.dtr-modal .modal-title {
    font-weight: 600;
}

/* Responsive table styling */
.table-responsive {
    border: none;
}

/* DataTable responsive breakpoints */
@media (max-width: 767.98px) {
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter {
        text-align: left;
        margin-bottom: 0.5rem;
    }
    
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        text-align: center;
        margin-top: 0.5rem;
    }
    
    /* Hide less important columns on mobile */
    .table-responsive .dtr-hidden {
        display: none !important;
    }
    
    /* Improve button spacing in actions column */
    .btn-sm {
        margin: 0.1rem;
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}

/* Responsive details styling */
.dtr-details {
    width: 100%;
}

.dtr-details li {
    border-bottom: 1px solid #dee2e6;
    padding: 12px 0 !important;
    margin: 0 !important;
}

.dtr-details li:last-child {
    border-bottom: none;
}

.dtr-details td {
    padding: 12px;
}

.dtr-title {
    font-weight: 600;
    color: #495057;
    min-width: 120px;
    display: inline-block;
}

.dtr-data {
    color: #6c757d;
}

/* Improve badge display in responsive mode */
.dtr-details .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* Responsive image handling */
.dtr-details img {
    max-width: 100%;
    height: auto;
}

/* Responsive button group */
.dtr-details .btn-group {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.dtr-details .btn-group .btn {
    flex: 1;
    min-width: 60px;
}

/* Responsive table header */
.table thead th {
    white-space: nowrap;
    vertical-align: middle;
}

/* Responsive table body */
.table tbody td {
    vertical-align: middle;
}

/* Responsive search box */
.dataTables_filter input {
    width: 100%;
    max-width: 200px;
}

/* Responsive pagination */
.dataTables_paginate .paginate_button {
    padding: 0.25rem 0.5rem;
    margin: 0 0.1rem;
    font-size: 0.875rem;
}

/* Responsive info text */
.dataTables_info {
    font-size: 0.875rem;
}

/* Responsive length selector */
.dataTables_length select {
    width: auto;
    min-width: 60px;
}

/* Improve modal responsiveness */
@media (max-width: 575.98px) {
    .dtr-modal .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
    }
    
    .dtr-modal .modal-body {
        padding: 1rem;
    }
    
    .dtr-modal .modal-footer {
        padding: 0.75rem 1rem;
    }
}

/* Dark mode support for responsive details */
@media (prefers-color-scheme: dark) {
    .dtr-details li {
        border-bottom-color: #495057;
    }
    
    .dtr-title {
        color: #e9ecef;
    }
    
    .dtr-data {
        color: #adb5bd;
    }
}

/* Print styles for responsive tables */
@media print {
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        display: none !important;
    }
    
    .table-responsive {
        overflow: visible !important;
    }
    
    .dtr-details {
        display: block !important;
    }
} 