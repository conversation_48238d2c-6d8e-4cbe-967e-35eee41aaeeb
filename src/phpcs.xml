<?xml version="1.0"?>
<ruleset name="Ifingo API">
    <description>Custom coding standards for Ifingo API project</description>

    <!-- Include PSR2 standard -->
    <rule ref="PSR2"/>

    <!-- Allow snake_case method names in test files -->
    <rule ref="PSR1.Methods.CamelCapsMethodName">
        <exclude-pattern>*/tests/*</exclude-pattern>
    </rule>

    <!-- Exclude vendor and other directories -->
    <exclude-pattern>vendor/*</exclude-pattern>
    <exclude-pattern>storage/*</exclude-pattern>
    <exclude-pattern>database/*</exclude-pattern>
    <exclude-pattern>bootstrap/cache/*</exclude-pattern>
    <exclude-pattern>public/vendor/*</exclude-pattern>

    <!-- Show progress and source codes -->
    <arg value="psn"/>
</ruleset>
