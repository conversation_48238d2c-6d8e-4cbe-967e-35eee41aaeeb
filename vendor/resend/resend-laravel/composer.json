{"name": "resend/resend-laravel", "description": "Resend for Laravel", "keywords": ["php", "resend", "laravel", "sdk", "api", "client"], "homepage": "https://resend.com/", "license": "MIT", "authors": [{"name": "Resend and contributors", "homepage": "https://github.com/resend/resend-laravel/contributors"}], "require": {"php": "^8.1", "illuminate/http": "^10.0|^11.0|^12.0", "illuminate/support": "^10.0|^11.0|^12.0", "resend/resend-php": "^0.18.0", "symfony/mailer": "^6.2|^7.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.14", "mockery/mockery": "^1.5", "orchestra/testbench": "^8.17|^9.0|^10.0", "pestphp/pest": "^2.0|^3.7"}, "autoload": {"psr-4": {"Resend\\Laravel\\": "src/"}}, "autoload-dev": {"psr-4": {"Resend\\Laravel\\Tests\\": "tests/"}}, "extra": {"branch-alias": {"dev-main": "1.x-dev"}, "laravel": {"providers": ["Resend\\Laravel\\ResendServiceProvider"]}}, "config": {"sort-packages": true, "preferred-install": "dist", "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}