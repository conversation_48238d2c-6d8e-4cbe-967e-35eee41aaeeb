image: atlassian/default-image:2
definitions:
  steps:
    - step: &sync-deploy
        name: Build & Deploy 
        script:
                - echo  $SSH_USER
                - echo $SSH_SERVER
                - echo "[INFO] Prepare .env file..."
                - cp src/.env.example src/.env
                - sed -i "s|MAIL_MAILER=.*|MAIL_MAILER=$MAIL_MAILER|" src/.env
                - sed -i "s|MAIL_HOST=.*|MAIL_HOST=$MAIL_HOST|" src/.env
                - sed -i "s|MAIL_PORT=.*|MAIL_PORT=$MAIL_PORT|" src/.env
                - sed -i "s|MAIL_USERNAME=.*|MAIL_USERNAME=$MAIL_USERNAME|" src/.env
                - sed -i "s|MAIL_PASSWORD=.*|MAIL_PASSWORD=$MAIL_PASSWORD|" src/.env
                - sed -i "s|MAIL_ENCRYPTION=.*|MAIL_ENCRYPTION=$MAIL_ENCRYPTION|" src/.env
                - sed -i "s|MAIL_FROM_ADDRESS=.*|MAIL_FROM_ADDRESS=$MAIL_FROM_ADDRESS|" src/.env
                - sed -i "s|MAIL_FROM_NAME=.*|MAIL_FROM_NAME=$MAIL_FROM_NAME|" src/.env
                - pipe: atlassian/rsync-deploy:0.6.0
                  variables:
                    USER: $SSH_USER
                    SERVER: $SSH_SERVER
                    REMOTE_PATH: '/home/<USER>/campzen-be'
                    LOCAL_PATH: '$BITBUCKET_CLONE_DIR/'
                    DEBUG: 'true'
                    EXTRA_ARGS: '-uv -c ${BITBUCKET_CLONE_DIR}/ '
                - |
                  ssh -o StrictHostKeyChecking=no $SSH_USER@$SSH_SERVER << EOF
                    set -e  # Exit immediately if a command exits with a non-zero status        
                    docker exec app composer install --no-dev --optimize-autoloader
                    docker exec app php artisan migrate
                    docker exec app php artisan config:clear
                    docker exec app php artisan route:clear
                    docker exec app php artisan config:cache
                    docker exec app php artisan route:cache
                  EOF

pipelines:
  branches:
    develop:
      - step:
          name: Build and deploy application to Dev 
          <<: *sync-deploy
          deployment: Dev

  custom: 
    Dev:
      - step:
          name: Build and deploy application to Dev
          <<: *sync-deploy
          deployment: Dev 